[versions]
agp = "8.10.1"
flexbox = "3.0.0"
hiltAndroid = "2.55"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
kotlinxCoroutinesAndroid = "1.7.3"
material = "1.12.0"
constraintlayout = "2.2.0"
lifecycleLivedataKtx = "2.8.7"
lifecycleViewmodelKtx = "2.8.7"
navigationFragment = "2.8.6"
navigationUi = "2.8.6"
activity = "1.8.0"
roomCommon = "2.6.1"
roomRuntime = "2.6.1"
roomCompiler = "2.6.1"

[libraries]
flexbox = { module = "com.google.android.flexbox:flexbox", version.ref = "flexbox" }
hilt-android = { module = "com.google.dagger:hilt-android", version = "2.56" }
hilt-android-compiler = { module = "com.google.dagger:hilt-android-compiler", version = "2.56" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version = "1.10.1" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version = "1.10.1" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version = "2.2.1" }
lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version = "2.8.9" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version = "2.8.9" }
activity = { group = "androidx.activity", name = "activity", version = "1.10.1" }
room-common = { group = "androidx.room", name = "room-common", version.ref = "roomCommon" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomCommon" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "roomRuntime" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "roomCompiler" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }

