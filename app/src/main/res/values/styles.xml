<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="隐藏小白条">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="软键盘按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">54dp</item>
        <item name="android:layout_columnWeight">1</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="记账选项按键样式">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:background">@drawable/widget_small_button_bg</item>
        <item name="backgroundTint">@color/white</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- 底部对话框样式 -->
    <style name="CustomBottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheet</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustNothing</item>
    </style>

    <style name="CustomBottomSheet" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
        <item name="backgroundTint">@android:color/white</item>
        <item name="android:elevation">16dp</item>
    </style>

    <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">20dp</item>
        <item name="cornerSizeTopLeft">20dp</item>
    </style>

    <!-- DatePicker样式 -->
    <style name="DatePickerStyle" parent="android:Widget.Material.DatePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style>

    <!-- TimePicker样式 -->
    <style name="TimePickerStyle" parent="android:Widget.Material.TimePicker">
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textSize">16sp</item>
        <item name="colorControlNormal">@color/HuaQing</item>
        <item name="colorAccent">@color/HuaQing</item>
    </style>

    <!-- 圆形图标遮罩样式 -->
    <style name="circleIconStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
</resources>