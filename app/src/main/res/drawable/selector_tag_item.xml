<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/ChaHuaHong" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/ChaHuaHong" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/light_grey" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/grey" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/light_grey" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/grey" />
        </shape>
    </item>
</selector>