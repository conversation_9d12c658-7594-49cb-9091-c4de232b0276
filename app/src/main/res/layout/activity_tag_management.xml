<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai">

    <!-- 标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="4dp">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/icon_close"
            android:padding="12dp"
            android:background="?android:attr/selectableItemBackground"
            android:contentDescription="返回"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="标签管理"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/iv_add_tag"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/icon_add"
            android:padding="12dp"
            android:background="?android:attr/selectableItemBackground"
            android:contentDescription="添加标签"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tag_management"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_tag_management_category" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/ll_empty_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无标签"
            android:textSize="16sp"
            android:textColor="@color/grey"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="点击右上角添加标签"
            android:textSize="14sp"
            android:textColor="@color/grey" />

    </LinearLayout>

</LinearLayout>