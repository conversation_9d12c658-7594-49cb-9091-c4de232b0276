<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="10dp"
    android:orientation="horizontal"
    android:layout_gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/wallets_account_type_item_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:padding="1dp"
        app:shapeAppearanceOverlay="@style/circleIconStyle"
        app:strokeColor="@color/YinBai"
        app:strokeWidth="2dp" />

    <TextView
        android:id="@+id/wallets_account_type_item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginEnd="10dp"
        android:layout_gravity="center_vertical"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:layout_marginStart="16dp"
        app:drawableEndCompat="@drawable/icon_arrow_right"
        app:drawableTint="@color/grey" />

</LinearLayout>