<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/white"
    android:layout_marginBottom="16dp"
    android:padding="16dp"
    android:elevation="2dp">

    <!-- 分类标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:id="@+id/tv_category_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="交通"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            tools:text="交通" />

        <TextView
            android:id="@+id/tv_add_tag_to_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加"
            android:textSize="14sp"
            android:textColor="@color/ChaHuaHong"
            android:padding="8dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

    <!-- 标签网格 -->
    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/flexbox_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:flexWrap="wrap"
        app:alignItems="flex_start"
        app:justifyContent="flex_start" />

</LinearLayout>