<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:fitsSystemWindows="true">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/initViewPager_tabs"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/YinBai"
        android:contentDescription="@string/receipt_desc_主界面导航栏"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tabBackground="@null"
        app:tabGravity="fill"
        app:tabIndicatorHeight="0dp"
        app:tabMinWidth="0dp"
        app:tabMode="fixed"
        app:tabRippleColor="@null" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/initViewPager_fragments"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/initViewPager_tabs"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>