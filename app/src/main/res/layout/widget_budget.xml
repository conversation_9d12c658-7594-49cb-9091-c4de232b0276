<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".widgets.Widget_InAndOut">

    <TextView
        android:id="@+id/widget_budget_剩余预算"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="15dp"
        android:text="@string/budget_剩余预算"
        android:textColor="@color/ChaHuaHong"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/widget_budget_剩余预算金额"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:text="￥1,000.00"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/widget_budget_总预算"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_总预算"
            android:layout_marginTop="8dp"
            android:layout_marginStart="15dp"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/widget_budget_总预算金额"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="￥2,500.00"
            android:layout_marginTop="8dp"
            android:layout_marginStart="5dp"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>