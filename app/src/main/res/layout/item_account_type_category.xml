<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/wallets_account_type_category_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        android:padding="6dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/expand_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/wallets_account_type_item_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/widget_common_bg"
        app:layout_constraintTop_toBottomOf="@id/wallets_account_type_category_name" />

</androidx.constraintlayout.widget.ConstraintLayout>