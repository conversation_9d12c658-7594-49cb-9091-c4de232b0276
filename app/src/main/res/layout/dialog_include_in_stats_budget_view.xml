<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recording_page_includeInStatsAndBudgetView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 标题栏 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:background="#DDDDDD"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/IncludeInStatsBudget_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/确定"
        android:textColor="@color/HuaQing"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/drag_handle" />

    <TextView
        android:id="@+id/IncludeInStatsBudget_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/取消"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/IncludeInStatsBudget_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/IncludeInStatsBudget_confirm" />

    <CheckedTextView
        android:id="@+id/recording_page_includeInStats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:checkMark="?android:attr/listChoiceIndicatorMultiple"
        android:checkMarkTint="@color/HuaQing"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:padding="10dp"
        android:text="@string/不计入收支"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/IncludeInStatsBudget_confirm" />

    <CheckedTextView
        android:id="@+id/recording_page_includeInBudget"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:checkMark="?android:attr/listChoiceIndicatorMultiple"
        android:checkMarkTint="@color/HuaQing"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:padding="10dp"
        android:text="@string/不计入预算"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recording_page_includeInStats" />

</androidx.constraintlayout.widget.ConstraintLayout>