<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/receipt_Daily_InOut_icon"
        android:contentDescription="@string/receipt_desc_收支示意圆点"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:src="@drawable/widget_icon_dot"
        app:tint="@color/ChaHuaHong" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/receipt_Daily_InOut_icon"
        app:layout_constraintRight_toLeftOf="@id/receipt_Daily_InOut_AmountAndAccount">

        <TextView
            android:id="@+id/receipt_Daily_InOut_kind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="公共交通"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/receipt_Daily_InOut_remark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="金融高新区-猎德"
            android:textSize="12sp" />

        <LinearLayout
            android:id="@+id/receipt_Daily_InOut_taglist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/receipt_Daily_InOut_AmountAndAccount"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/receipt_Daily_InOut_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:text="-¥100.00"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textColor="@color/ChaHuaHong" />

        <TextView
            android:id="@+id/receipt_Daily_InOut_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:text="中国银行储蓄卡521"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>