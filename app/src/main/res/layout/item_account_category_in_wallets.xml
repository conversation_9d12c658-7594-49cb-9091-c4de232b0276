<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginHorizontal="15dp"
    android:layout_marginTop="10dp">

    <LinearLayout
        android:id="@+id/account_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center_vertical">

        <TextView
            android:id="@+id/tv_account_category_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="银行卡" />

        <TextView
            android:id="@+id/tv_account_category_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|end"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="10,000.00" />

        <ImageView
            android:id="@+id/iv_account_expand_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="15dp"
        app:cardElevation="0dp"
        android:layout_marginTop="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_account_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>