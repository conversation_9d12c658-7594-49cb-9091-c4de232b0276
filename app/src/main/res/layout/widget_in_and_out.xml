<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".widgets.Widget_InAndOut">

    <TextView
        android:id="@+id/widget_in_out_总支出"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="15dp"
        android:text="@string/in_out_总支出"
        android:textColor="@color/ChaHuaHong"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/widget_in_out_总支出金额"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:text="￥2,000.00"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/widget_in_out_总收入"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/in_out_总收入"
            android:layout_marginTop="8dp"
            android:layout_marginStart="15dp"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/widget_in_out_总收入金额"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="￥5,000.00"
            android:layout_marginTop="8dp"
            android:layout_marginStart="5dp"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/widget_in_out_结余"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/in_out_结余"
            android:layout_marginTop="8dp"
            android:layout_marginStart="30dp"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/widget_in_out_结余金额"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="￥3,000.00"
            android:layout_marginTop="8dp"
            android:layout_marginStart="5dp"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>