<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:id="@+id/account_item_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_account_item_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="1dp"
            app:shapeAppearanceOverlay="@style/circleIconStyle"
            app:strokeColor="@color/YinBai"
            app:strokeWidth="1dp"
            tools:src="@drawable/icon_bank_icbc" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_account_item_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="工商银行储蓄卡" />

            <TextView
                android:id="@+id/tv_account_item_due_time_remained"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="3天后还款"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_account_item_remark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/grey"
                android:textSize="13sp"
                android:visibility="gone"
                tools:text="工资卡"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_account_item_includeInAsset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/widget_tag_item_bg"
                android:backgroundTint="@color/grey"
                android:paddingStart="5dp"
                android:paddingTop="2dp"
                android:paddingEnd="5dp"
                android:paddingBottom="2dp"
                android:text="@string/wallet_账户不计入资产"
                android:textSize="11sp"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_vertical|end">

            <TextView
                android:id="@+id/tv_account_item_balance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="¥5,000.00" />

            <TextView
                android:id="@+id/tv_account_item_credit_remained"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:textSize="14sp"
                tools:text="可用额度：¥10,000.00" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>