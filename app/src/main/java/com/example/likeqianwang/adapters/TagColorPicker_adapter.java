package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.R;

import java.util.List;

public class TagColorPicker_adapter extends RecyclerView.Adapter<TagColorPicker_adapter.ColorViewHolder>{
    private final Context context;
    private final List<String> colors;
    private String selectedColor;
    private OnColorSelectedListener listener;

    public TagColorPicker_adapter(Context context, List<String> colors, String selectedColor) {
        this.context = context;
        this.colors = colors;
        this.selectedColor = selectedColor;
    }

    @NonNull
    @Override
    public ColorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.style_tag_color_picker, parent, false);
        return new ColorViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ColorViewHolder holder, int position) {
        String color = colors.get(position);
        holder.bind(color);
    }

    @Override
    public int getItemCount() {
        return colors.size();
    }

    public void setSelectedColor(String color) {
        this.selectedColor = color;
        notifyDataSetChanged();
    }

    public void setOnColorSelectedListener(OnColorSelectedListener listener) {
        this.listener = listener;
    }

    class ColorViewHolder extends RecyclerView.ViewHolder {
        private final View viewColor;
        private final ImageView ivSelected;

        public ColorViewHolder(@NonNull View itemView) {
            super(itemView);
            viewColor = itemView.findViewById(R.id.view_color);
            ivSelected = itemView.findViewById(R.id.iv_selected);
        }

        public void bind(String color) {
            try {
                // 设置颜色
                GradientDrawable drawable = new GradientDrawable();
                drawable.setShape(GradientDrawable.OVAL);
                drawable.setColor(Color.parseColor(color));
                drawable.setStroke(4, Color.WHITE);
                viewColor.setBackground(drawable);

                // 设置选中状态
                boolean isSelected = color.equals(selectedColor);
                ivSelected.setVisibility(isSelected ? View.VISIBLE : View.GONE);

                // 设置点击事件
                itemView.setOnClickListener(v -> {
                    selectedColor = color;
                    notifyDataSetChanged();
                    if (listener != null) {
                        listener.onColorSelected(color);
                    }
                });

            } catch (IllegalArgumentException e) {
                // 颜色格式错误，使用默认颜色
                viewColor.setBackgroundResource(R.drawable.style_tag_color_picker_bg);
            }
        }
    }

    public interface OnColorSelectedListener {
        void onColorSelected(String color);
    }

}
