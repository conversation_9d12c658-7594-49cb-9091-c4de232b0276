package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.StyleRecordingCategoryItemViewBinding;

import java.util.List;

public class RecordingDialogSubcategory_adapter extends RecyclerView.Adapter<RecordingDialogSubcategory_adapter.SubcategoryViewHolder> {
    private final Context context;
    private final List<TransactionSubcategory> subcategories;
    private int selectedPosition = RecyclerView.NO_POSITION;
    private OnSubcategoryClickListener listener;

    public interface OnSubcategoryClickListener {
        void onSubcategoryClick(TransactionSubcategory subcategory, int position);
    }

    public RecordingDialogSubcategory_adapter(Context context, List<TransactionSubcategory> subcategories) {
        this.context = context;
        this.subcategories = subcategories;
    }

    public void setOnSubcategoryClickListener(OnSubcategoryClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public SubcategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        StyleRecordingCategoryItemViewBinding recordingSubcategoryItemViewBinding = StyleRecordingCategoryItemViewBinding.inflate(LayoutInflater.from(context), parent, false);
        return new SubcategoryViewHolder(recordingSubcategoryItemViewBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull SubcategoryViewHolder holder, int position) {
        TransactionSubcategory subcategory = subcategories.get(position);

        // 设置子类别信息
        holder.subcategoryIcon.setImageResource(subcategory.getSubcategoryIcon());
        holder.subcategoryName.setText(subcategory.getSubcategoryName());

        // 设置选中状态
        boolean isSelected = position == selectedPosition;
        if (isSelected) {
            holder.subcategoryIcon.setBackground(ContextCompat.getDrawable(context, R.drawable.recyclerview_item_selected_bg));
            holder.subcategoryName.setTextColor(ContextCompat.getColor(context, R.color.HuaQing));
            // 设置图标为选中颜色
            holder.subcategoryIcon.setColorFilter(ContextCompat.getColor(context, R.color.white));
        } else {
            holder.subcategoryIcon.setBackground(null);
            holder.subcategoryName.setTextColor(ContextCompat.getColor(context, R.color.defaultGrey));
            // 清除图标颜色过滤器
            holder.subcategoryIcon.clearColorFilter();
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            // 获取当前最新的位置，而不是使用传入的position
            int currentPosition = holder.getAdapterPosition();
            if (currentPosition == RecyclerView.NO_POSITION) {
                return; // 位置无效，不处理
            }

            int previousSelected = selectedPosition;
            selectedPosition = currentPosition;

            // 通知适配器更新之前选中的项和当前选中的项
            if (previousSelected != RecyclerView.NO_POSITION) {
                notifyItemChanged(previousSelected);
            }
            notifyItemChanged(selectedPosition);

            // 回调监听器
            if (listener != null) {
                listener.onSubcategoryClick(subcategories.get(currentPosition), currentPosition);
            }
        });
    }

    @Override
    public int getItemCount() {
        return subcategories.size();
    }

    // 获取当前选中的位置
    public int getSelectedPosition() {
        return selectedPosition;
    }

    // 获取当前选中的子类别
    public TransactionSubcategory getSelectedSubcategory() {
        if (selectedPosition != RecyclerView.NO_POSITION && selectedPosition < subcategories.size()) {
            return subcategories.get(selectedPosition);
        }
        return null;
    }

    // 设置选中的位置
    public void setSelectedPosition(int position) {
        if (position >= 0 && position < subcategories.size()) {
            int previousSelected = selectedPosition;
            selectedPosition = position;

            if (previousSelected != RecyclerView.NO_POSITION) {
                notifyItemChanged(previousSelected);
            }
            notifyItemChanged(selectedPosition);
        }
    }

    // 根据子类别ID设置选中项
    public void setSelectedSubcategoryById(long subcategoryId) {
        for (int i = 0; i < subcategories.size(); i++) {
            if (subcategories.get(i).getSubcategoryId() == subcategoryId) {
                setSelectedPosition(i);
                break;
            }
        }
    }

    public static class SubcategoryViewHolder extends RecyclerView.ViewHolder {
        ImageView subcategoryIcon;
        TextView subcategoryName;

        public SubcategoryViewHolder(@NonNull StyleRecordingCategoryItemViewBinding recordingSubcategoryItemViewBinding) {
            super(recordingSubcategoryItemViewBinding.getRoot());
            subcategoryIcon = recordingSubcategoryItemViewBinding.ivRecordingCategoryItemIcon;
            subcategoryName = recordingSubcategoryItemViewBinding.tvRecordingCategoryItemName;
        }
    }
}
