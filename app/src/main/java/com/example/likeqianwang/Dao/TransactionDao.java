package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Dao
public interface TransactionDao {

    @Insert
    long insert(Transactions transactions);

    @Delete
    void delete(Transactions transactions);

    @Update
    void update(Transactions transactions);

    @Query("SELECT * FROM Transactions WHERE transactionId = :transactionId")
    Transactions getById(long transactionId);

    @Query("SELECT * FROM Transactions WHERE transactionDate BETWEEN :startDate AND :endDate")
    LiveData<List<Transactions>> getByDateRange(Date startDate, Date endDate);

    @Query("SELECT SUM(amount) FROM Transactions " +
            "WHERE type = :type AND transactionDate BETWEEN :startDate AND :endDate " +
            "AND include_in_stats = 1")
    BigDecimal getSum(String type, Date startDate, Date endDate);
}

