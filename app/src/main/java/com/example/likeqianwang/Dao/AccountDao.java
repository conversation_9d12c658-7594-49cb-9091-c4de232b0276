package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.Account;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户数据访问对象
 */
@Dao
public interface AccountDao {
    /**
     * 插入账户
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Account account);

    /**
     * 更新账户
     */
    @Update
    void update(Account account);

    /**
     * 删除账户
     */
    @Delete
    void delete(Account account);

    // 添加同步获取所有账户的方法
    @Query("SELECT * FROM transaction_accounts")
    List<Account> getAllAccountsSync();

    /**
     * 根据ID获取账户
     */
    @Query("SELECT * FROM transaction_accounts WHERE accountId = :accountId")
    LiveData<Account> getAccountById(String accountId);

    /**
     * 获取所有账户
     */
    @Query("SELECT * FROM transaction_accounts ORDER BY createTime DESC")
    LiveData<List<Account>> getAllAccounts();

    /**
     * 获取所有计入总资产的账户
     */
    @Query("SELECT * FROM transaction_accounts WHERE includeInAsset = 1 ORDER BY createTime DESC")
    LiveData<List<Account>> getIncludedAccounts();

    /**
     * 根据账户类型获取账户
     */
    @Query("SELECT * FROM transaction_accounts WHERE accountTypeCategoryId = :categoryId ORDER BY createTime DESC")
    LiveData<List<Account>> getAccountsByCategoryId(String categoryId);

    /**
     * 获取所有借记卡/储蓄卡账户
     */
    @Query("SELECT * FROM transaction_accounts WHERE accountTypeDebitCredit = 1 ORDER BY createTime DESC")
    LiveData<List<Account>> getDebitAccounts();

    /**
     * 获取所有信用卡账户
     */
    @Query("SELECT * FROM transaction_accounts WHERE accountTypeDebitCredit = 0 ORDER BY createTime DESC")
    LiveData<List<Account>> getCreditAccounts();

    @Query("SELECT DISTINCT accountTypeCategoryId FROM transaction_accounts")
    List<String> getAllAccountTypeCategories();
}
