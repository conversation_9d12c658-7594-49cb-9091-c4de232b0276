package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionSubcategory;

import java.util.List;

@Dao
public interface TransactionSubcategoryDao {
    @Insert
    long insert(TransactionSubcategory subcategory);

    @Update
    void update(TransactionSubcategory subcategory);

    @Delete
    void delete(TransactionSubcategory subcategory);

    @Query("SELECT * FROM transaction_subcategories WHERE parentCategoryId = :parentCategoryId ORDER BY orderIndex ASC")
    LiveData<List<TransactionSubcategory>> getSubcategoriesByParentId(long parentCategoryId);

    @Query("SELECT * FROM transaction_subcategories WHERE parentCategoryId = :parentCategoryId ORDER BY orderIndex ASC")
    List<TransactionSubcategory> getSubcategoriesByParentIdSync(long parentCategoryId);

    @Query("SELECT * FROM transaction_subcategories WHERE subcategoryId = :subcategoryId")
    TransactionSubcategory getSubcategoryById(long subcategoryId);
}
