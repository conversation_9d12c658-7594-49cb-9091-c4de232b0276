package com.example.likeqianwang.interfaces;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;

/**
 * 录制页面数据提供接口
 * 用于从各个Fragment获取选中的分类、账户等信息
 */
public interface RecordingDataProvider {
    
    /**
     * 获取选中的分类
     * @return 选中的分类，如果没有选中则返回null
     */
    TransactionCategory getSelectedCategory();
    
    /**
     * 获取选中的子分类
     * @return 选中的子分类，如果没有选中则返回null
     */
    TransactionSubcategory getSelectedSubcategory();
    
    /**
     * 获取转出账户（仅转账时使用）
     * @return 转出账户，如果没有选中则返回null
     */
    Account getFromAccount();
    
    /**
     * 获取转入账户（仅转账时使用）
     * @return 转入账户，如果没有选中则返回null
     */
    Account getToAccount();
    
    /**
     * 获取转账类型（仅转账时使用）
     * @return 转账类型（转账、还款、提现）
     */
    String getTransferType();
    
    /**
     * 检查是否有必要的数据被选中
     * @return 如果有必要数据被选中返回true，否则返回false
     */
    boolean hasRequiredData();
}
