package com.example.likeqianwang;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.ViewModel.TransactionTagViewModel;
import com.example.likeqianwang.adapters.TagManagement_adapter;
import com.example.likeqianwang.ui.TagEditDialog;

import java.util.ArrayList;
import java.util.List;

public class TagManagementActivity extends AppCompatActivity {
    private static final String TAG = "TagManagementActivity";
    private TransactionTagViewModel viewModel;
    private TagManagement_adapter adapter;
    private RecyclerView rvTagManagement;
    private LinearLayout llEmptyState;
    private ImageView ivBack;
    private ImageView ivAddTag;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Starting TagManagementActivity");

        try {
            setContentView(R.layout.activity_tag_management);
            Log.d(TAG, "onCreate: Layout set successfully");

            initViews();
            Log.d(TAG, "onCreate: Views initialized");

            initViewModel();
            Log.d(TAG, "onCreate: ViewModel initialized");

            setupRecyclerView();
            Log.d(TAG, "onCreate: RecyclerView setup complete");

            setupObservers();
            Log.d(TAG, "onCreate: Observers setup complete");

            setupClickListeners();
            Log.d(TAG, "onCreate: Click listeners setup complete");

        } catch (Exception e) {
            Log.e(TAG, "onCreate: Error during initialization", e);
            finish();
        }
    }

    private void initViews() {
        try {
            rvTagManagement = findViewById(R.id.rv_tag_management);
            Log.d(TAG, "initViews: rvTagManagement found: " + (rvTagManagement != null));

            llEmptyState = findViewById(R.id.ll_empty_state);
            Log.d(TAG, "initViews: llEmptyState found: " + (llEmptyState != null));

            ivBack = findViewById(R.id.iv_back);
            Log.d(TAG, "initViews: ivBack found: " + (ivBack != null));

            ivAddTag = findViewById(R.id.iv_add_tag);
            Log.d(TAG, "initViews: ivAddTag found: " + (ivAddTag != null));

        } catch (Exception e) {
            Log.e(TAG, "initViews: Error finding views", e);
            throw e;
        }
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(TransactionTagViewModel.class);
    }

    private void setupRecyclerView() {
        rvTagManagement.setLayoutManager(new LinearLayoutManager(this));
        adapter = new TagManagement_adapter(this, new ArrayList<>(), null);
        adapter.setOnTagActionListener(new TagManagement_adapter.OnTagActionListener() {
            @Override
            public void onAddTagToCategory(String category) {
                showTagEditDialog(null, category);
            }

            @Override
            public void onEditTag(TransactionTag tag) {
                showTagEditDialog(tag, null);
            }

            @Override
            public void onDeleteTag(TransactionTag tag) {
                showDeleteConfirmDialog(tag);
            }
        });
        rvTagManagement.setAdapter(adapter);
    }

    private void setupObservers() {
        // 观察分类化的标签数据
        viewModel.getCategorizedTags().observe(this, categorizedTags -> {
            try {
                if (categorizedTags != null && !categorizedTags.isEmpty()) {
                    List<String> categories = new ArrayList<>(categorizedTags.keySet());
                    if (adapter != null) {
                        adapter.updateData(categories, categorizedTags);
                    }
                    showContent();
                } else {
                    showEmptyState();
                }
            } catch (Exception e) {
                e.printStackTrace();
                showEmptyState();
            }
        });

        // 观察操作状态
        viewModel.getOperationStatus().observe(this, status -> {
            try {
                if (status != null && !status.isEmpty()) {
                    Toast.makeText(this, status, Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        // 观察加载状态
        viewModel.getIsLoading().observe(this, isLoading -> {
            try {
                // 可以在这里显示/隐藏加载指示器
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());

        ivAddTag.setOnClickListener(v -> showTagEditDialog(null, null));
    }

    private void showContent() {
        rvTagManagement.setVisibility(View.VISIBLE);
        llEmptyState.setVisibility(View.GONE);
    }

    private void showEmptyState() {
        rvTagManagement.setVisibility(View.GONE);
        llEmptyState.setVisibility(View.VISIBLE);
    }

    private void showTagEditDialog(TransactionTag tag, String defaultCategory) {
        TagEditDialog dialog = new TagEditDialog(this, tag, defaultCategory);
        dialog.setOnTagSaveListener(new TagEditDialog.OnTagSaveListener() {
            @Override
            public void onTagSaved(TransactionTag savedTag, boolean isEdit) {
                if (isEdit) {
                    viewModel.updateTag(savedTag);
                } else {
                    viewModel.addTag(savedTag);
                }
            }
        });
        dialog.show();
    }

    private void showDeleteConfirmDialog(TransactionTag tag) {
        new AlertDialog.Builder(this)
                .setTitle("删除标签")
                .setMessage("确定要删除标签 \"" + tag.getTagName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    viewModel.deleteTag(tag);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 刷新数据
        viewModel.refresh();
    }

}
