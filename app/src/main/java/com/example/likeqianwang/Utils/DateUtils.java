package com.example.likeqianwang.Utils;

import java.util.Calendar;

public class DateUtils {
    /**
     * 计算距离下一个指定日期的天数
     * @param targetDay 目标日期（每月的第几天）
     * @return 距离下一个目标日期的天数
     */
    public static int getDaysUntilNextDate(int targetDay) {
        Calendar today = Calendar.getInstance();
        Calendar targetDate = Calendar.getInstance();

        // 设置目标日期
        targetDate.set(Calendar.DAY_OF_MONTH, targetDay);

        // 如果当月的目标日期已过，计算下个月的目标日期
        if (today.get(Calendar.DAY_OF_MONTH) > targetDay) {
            targetDate.add(Calendar.MONTH, 1);
        }

        // 计算相差的天数
        long diffMillis = targetDate.getTimeInMillis() - today.getTimeInMillis();
        return (int) (diffMillis / (24 * 60 * 60 * 1000));
    }

    /**
     * 获取信用卡还款日提示文本
     * @param dueDay 还款日
     * @return 还款日提示文本
     */
    public static String getDueDateReminderText(int dueDay) {
        int daysUntilDue = getDaysUntilNextDate(dueDay);

        if (daysUntilDue == 0) {
            return "今天还款";
        } else if (daysUntilDue < 0) {
            return "已逾期" + Math.abs(daysUntilDue) + "天";
        } else {
            return daysUntilDue + "天后还款";
        }
    }
}
