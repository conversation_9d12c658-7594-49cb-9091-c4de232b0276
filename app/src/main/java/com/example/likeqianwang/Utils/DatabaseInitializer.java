package com.example.likeqianwang.Utils;

import android.content.Context;
import android.util.Log;

import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class DatabaseInitializer {

    private final AppDatabase database;
    private final Executor executor = Executors.newSingleThreadExecutor();

    public DatabaseInitializer(Context context) {
        database = AppDatabase.getInstance(context);
    }

    public void initializeData() {
        executor.execute(() -> {
            // 检查数据库是否已经初始化
            if (database.transactionCategoryDao().getCategoriesCount() == 0) {
                // 初始化支出分类
                initializeExpenseCategories();

                // 初始化收入分类
                initializeIncomeCategories();
            }

            // 检查标签是否已经初始化
            if (database.transactionTagDao().getTagsCount() == 0) {
                // 初始化默认标签
                initializeDefaultTags();
            }
        });
    }

    private void initializeExpenseCategories() {
        List<TransactionCategory> expenseCategories = new ArrayList<>();

        // 创建支出分类
        TransactionCategory dietCategory = new TransactionCategory("饮食", R.drawable.icon_recording_diet, 0, true, 0);
        TransactionCategory transportationCategory = new TransactionCategory("交通", R.drawable.icon_recording_transportation, 0, true, 1);
        TransactionCategory clothesCategory = new TransactionCategory("服装", R.drawable.icon_recording_clothes, 0, false, 2);
        TransactionCategory medicineCategory = new TransactionCategory("医疗", R.drawable.icon_recording_medicine, 0, false, 3);
        TransactionCategory makeupCategory = new TransactionCategory("化妆", R.drawable.icon_recording_makeup, 0, false, 4);
        TransactionCategory cellphoneCategory = new TransactionCategory("话费网费", R.drawable.icon_recording_mobile_credit, 0, false, 5);
        TransactionCategory chargeCategory = new TransactionCategory("充电", R.drawable.icon_recording_charge, 0, false, 6);
        TransactionCategory educationCategory = new TransactionCategory("教育", R.drawable.icon_recording_education, 0, false, 7);
        TransactionCategory entertainmentCategory = new TransactionCategory("娱乐", R.drawable.icon_recording_entertainment, 0, false, 8);
        TransactionCategory expressCategory = new TransactionCategory("快递", R.drawable.icon_recording_express_delivery, 0, false, 9);
        TransactionCategory haircutCategory = new TransactionCategory("理发", R.drawable.icon_recording_haircut, 0, false, 10);
        TransactionCategory sportsCategory = new TransactionCategory("运动", R.drawable.icon_recording_sports, 0, false, 11);
        TransactionCategory travelCategory = new TransactionCategory("旅游", R.drawable.icon_recording_journey, 0, false, 12);

        // 添加到列表
        expenseCategories.add(dietCategory);
        expenseCategories.add(transportationCategory);
        expenseCategories.add(clothesCategory);
        expenseCategories.add(medicineCategory);
        expenseCategories.add(makeupCategory);
        expenseCategories.add(cellphoneCategory);
        expenseCategories.add(chargeCategory);
        expenseCategories.add(educationCategory);
        expenseCategories.add(entertainmentCategory);
        expenseCategories.add(expressCategory);
        expenseCategories.add(haircutCategory);
        expenseCategories.add(sportsCategory);
        expenseCategories.add(travelCategory);

        // 插入到数据库
        for (TransactionCategory category : expenseCategories) {
            long categoryId = database.transactionCategoryDao().insert(category);

            // 如果有子分类，添加子分类
            if (category.isHasSubCategories()) {
                List<TransactionSubcategory> subcategories = new ArrayList<>();

                if (category.getCategoryName().equals("饮食")) {
                    subcategories.add(new TransactionSubcategory("早餐", R.drawable.icon_recording_breakfast, categoryId, 0));
                    subcategories.add(new TransactionSubcategory("午餐", R.drawable.icon_recording_lunch, categoryId, 1));
                    subcategories.add(new TransactionSubcategory("晚餐", R.drawable.icon_recording_dinner, categoryId, 2));
                } else if (category.getCategoryName().equals("交通")) {
                    subcategories.add(new TransactionSubcategory("地铁", R.drawable.icon_recording_subway, categoryId, 0));
                    subcategories.add(new TransactionSubcategory("共享单车", R.drawable.icon_recording_bike, categoryId, 1));
                    subcategories.add(new TransactionSubcategory("打车", R.drawable.icon_recording_taxi, categoryId, 2));
                }

                // 插入子分类
                for (TransactionSubcategory subcategory : subcategories) {
                    database.transactionSubcategoryDao().insert(subcategory);
                }
            }
        }
    }

    private void initializeIncomeCategories() {
        List<TransactionCategory> incomeCategories = new ArrayList<>();

        // 创建收入分类
        TransactionCategory salaryCategory = new TransactionCategory("工资", R.drawable.icon_recording_salary, 1, true, 0);
        TransactionCategory investmentCategory = new TransactionCategory("投资", R.drawable.icon_recording_investment, 1, true, 1);
        TransactionCategory bonusCategory = new TransactionCategory("奖金", R.drawable.icon_recording_bonus, 1, false, 2);
        TransactionCategory tripAllowanceCategory = new TransactionCategory("差补", R.drawable.icon_recording_trip_allowance, 1, false, 3);
        TransactionCategory otherCategory = new TransactionCategory("其他", R.drawable.icon_recording_others, 1, false, 4);

        // 添加到列表
        incomeCategories.add(salaryCategory);
        incomeCategories.add(investmentCategory);
        incomeCategories.add(bonusCategory);
        incomeCategories.add(tripAllowanceCategory);
        incomeCategories.add(otherCategory);

        // 插入到数据库
        for (TransactionCategory category : incomeCategories) {
            long categoryId = database.transactionCategoryDao().insert(category);

            // 如果有子分类，添加子分类
            if (category.isHasSubCategories()) {
                List<TransactionSubcategory> subcategories = new ArrayList<>();

                if (category.getCategoryName().equals("投资")) {
                    subcategories.add(new TransactionSubcategory("股票", R.drawable.icon_recording_stock, categoryId, 0));
                    subcategories.add(new TransactionSubcategory("基金", R.drawable.icon_recording_fund, categoryId, 1));
                    subcategories.add(new TransactionSubcategory("利息", R.drawable.icon_recording_interest, categoryId, 2));
                }

                // 插入子分类
                for (TransactionSubcategory subcategory : subcategories) {
                    database.transactionSubcategoryDao().insert(subcategory);
                }
            }
        }
    }

    private void initializeDefaultTags() {
        List<TransactionTag> defaultTags = new ArrayList<>();

        // 交通类标签
        defaultTags.add(new TransactionTag("广州地铁", "#2196F3", "交通"));
        defaultTags.add(new TransactionTag("滴滴快车", "#4CAF50", "交通"));
        defaultTags.add(new TransactionTag("高德打车", "#FF9800", "交通"));
        defaultTags.add(new TransactionTag("佛山地铁", "#F44336", "交通"));
        defaultTags.add(new TransactionTag("拼车", "#FFC107", "交通"));
        defaultTags.add(new TransactionTag("巴士", "#FF5722", "交通"));

        // 购物类标签
        defaultTags.add(new TransactionTag("淘宝", "#E91E63", "购物"));
        defaultTags.add(new TransactionTag("京东", "#607D8B", "购物"));
        defaultTags.add(new TransactionTag("拼多多", "#009688", "购物"));
        defaultTags.add(new TransactionTag("盒马鲜生", "#FF4081", "购物"));

        // 其他类标签
        defaultTags.add(new TransactionTag("美团", "#9E9E9E", "餐饮"));
        defaultTags.add(new TransactionTag("饿了么", "#FFEB3B", "餐饮"));
        defaultTags.add(new TransactionTag("京东外卖", "#E91E63", "餐饮"));

        // 插入到数据库
        for (int i = 0; i < defaultTags.size(); i++) {
            TransactionTag tag = defaultTags.get(i);
            tag.setOrderIndex(i);
            database.transactionTagDao().insert(tag);
        }
    }
}
