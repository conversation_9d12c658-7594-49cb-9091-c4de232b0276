package com.example.likeqianwang.Utils;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager2.widget.ViewPager2;

public class NestedScrollableHost extends FrameLayout {

    private int touchSlop;
    private float initialX;
    private float initialY;

    @Nullable
    private ViewPager2 getParentViewPager() {
        View parentView = (View) getParent();
        while (parentView != null && !(parentView instanceof ViewPager2)) {
            parentView = (View) parentView.getParent();
        }
        return (ViewPager2) parentView;
    }

    @Nullable
    private View getChildView() {
        return getChildCount() > 0 ? getChildAt(0) : null;
    }

    public NestedScrollableHost(@NonNull Context context) {
        super(context);
        initialize(context);
    }

    public NestedScrollableHost(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initialize(context);
    }

    private void initialize(Context context) {
        touchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    private boolean canChildScroll(int orientation, float delta) {
        int direction = (int) -Math.signum(delta);
        switch (orientation) {
            case 0:
                return getChildView() != null && getChildView().canScrollHorizontally(direction);
            case 1:
                return getChildView() != null && getChildView().canScrollVertically(direction);
            default:
                throw new IllegalArgumentException();
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        handleInterceptTouchEvent(event);
        return super.onInterceptTouchEvent(event);
    }

    private void handleInterceptTouchEvent(MotionEvent event) {
        Integer orientation = getParentViewPager() != null ? getParentViewPager().getOrientation() : null;
        if (orientation == null) {
            return;
        }

        // Early return if child can't scroll in same direction as parent
        if (!canChildScroll(orientation, -1f) && !canChildScroll(orientation, 1f)) {
            return;
        }

        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            initialX = event.getX();
            initialY = event.getY();
            getParent().requestDisallowInterceptTouchEvent(true);
        } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
            float deltaX = event.getX() - initialX;
            float deltaY = event.getY() - initialY;
            boolean isViewPagerHorizontal = orientation == ViewPager2.ORIENTATION_HORIZONTAL;
            // assuming ViewPager2 touch-slope is 2x touch-slope of child
            float scaledDeltaX = Math.abs(deltaX) * (isViewPagerHorizontal ? 0.5f : 1f);
            float scaledDeltaY = Math.abs(deltaY) * (isViewPagerHorizontal ? 1f : 0.5f);

            if (scaledDeltaX > touchSlop || scaledDeltaY > touchSlop) {

                if (isViewPagerHorizontal == (scaledDeltaY > scaledDeltaX)) {
                    // Gesture is perpendicular, allow all parents to intercept
                    getParent().requestDisallowInterceptTouchEvent(false);
                } else {
                    // Gesture is parallel, query child if movement in that direction is possible
                    // Child can scroll, disallow all parents to intercept
                    // Child cannot scroll, allow all parents to intercept
                    getParent().requestDisallowInterceptTouchEvent(canChildScroll(orientation, isViewPagerHorizontal ? deltaX : deltaY));
                }
            }
        }
    }
}