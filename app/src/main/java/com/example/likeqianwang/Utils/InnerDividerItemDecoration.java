package com.example.likeqianwang.Utils;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class InnerDividerItemDecoration extends RecyclerView.ItemDecoration {
    private final Paint dividerPaint;
    private final int dividerHeight;
    private final int leftMargin;
    private final int rightMargin;

    public InnerDividerItemDecoration(Context context, int dividerColor, int dividerHeight, int leftMargin, int rightMargin) {
        this.dividerPaint = new Paint();
        this.dividerPaint.setColor(dividerColor);
        this.dividerHeight = dividerHeight;
        this.leftMargin = leftMargin;
        this.rightMargin = rightMargin;
    }

    @Override
    public void onDrawOver(@NonNull Canvas canvas, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        RecyclerView.Adapter adapter = parent.getAdapter();
        if (adapter == null) {
            return;
        }

        int left = parent.getPaddingLeft() + leftMargin;
        int right = parent.getWidth() - parent.getPaddingRight() - rightMargin;

        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            int position = parent.getChildAdapterPosition(child);

            // 跳过无效位置或最后一个项目
            if (position == RecyclerView.NO_POSITION || position == adapter.getItemCount() - 1) {
                continue;
            }

            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
            int top = child.getBottom() + params.bottomMargin;
            int bottom = top + dividerHeight;

            canvas.drawRect(left, top, right, bottom, dividerPaint);
        }
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        // 为所有项目添加底部间距，使分割线有足够的空间绘制
        int position = parent.getChildAdapterPosition(view);

        // 最后一项不需要底部间距
        if (position == parent.getAdapter().getItemCount() - 1) {
            outRect.bottom = 0;
        } else {
            outRect.bottom = dividerHeight;
        }
    }
}
