package com.example.likeqianwang.Utils;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

public class CurrencyFormatter {
    private static final DecimalFormat formatter = (DecimalFormat) NumberFormat.getCurrencyInstance(Locale.CHINA);

    static {
        formatter.setMaximumFractionDigits(2);
        formatter.setMinimumFractionDigits(2);
    }

    /**
     * 格式化金额为货币格式（带货币符号）
     * @param amount 金额
     * @return 格式化后的字符串
     */
    public static String format(double amount) {
        return formatter.format(amount);
    }

    /**
     * 格式化金额为货币格式（不带货币符号）
     * @param amount 金额
     * @return 格式化后的字符串
     */
    public static String formatWithoutSymbol(double amount) {
        String formatted = formatter.format(amount);
        // 移除货币符号
        return formatted.substring(1);
    }
}
