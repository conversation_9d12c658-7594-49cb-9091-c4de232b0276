package com.example.likeqianwang.Repository;

import androidx.room.Transaction;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;

import javax.inject.Inject;

public class TransactionRepository {
    private final AccountDao accountDao;
    private final TransactionDao transactionDao;
    private final TransactionCategoryDao categoryDao;
    private final TransactionTagDao transactionTagDao;
    private final TransactionTagCrossRefDao transactionTagCrossRefDao;

    @Inject
    public TransactionRepository(AppDatabase database) {
        this.transactionDao = database.transactionDao();
        this.accountDao = database.accountDao();
        this.categoryDao = database.transactionCategoryDao();
        this.transactionTagDao = database.transactionTagDao();
        this.transactionTagCrossRefDao = database.transactionTagCrossRefDao();
    }

    @Transaction
    public void addTransaction(Transactions transactions) {

    }
}
