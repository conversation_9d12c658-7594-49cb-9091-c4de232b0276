package com.example.likeqianwang.DataModel;

public class AccountTypeItem {
    private String id;
    private String name;
    private int iconResId;
    private String parentAccountTypeCategoryId;
    private int debitOrCredit;  // 1代表资产，0代表负债

    public AccountTypeItem(String id, String name, int iconResId, String parentAccountTypeCategoryId, int debitOrCredit) {
        this.id = id;
        this.name = name;
        this.iconResId = iconResId;
        this.parentAccountTypeCategoryId = parentAccountTypeCategoryId;
        this.debitOrCredit = debitOrCredit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIconResId() {
        return iconResId;
    }

    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }

    public String getParentAccountTypeCategoryId() {
        return parentAccountTypeCategoryId;
    }

    public void setParentAccountTypeCategoryId(String parentAccountTypeCategoryId) {
        this.parentAccountTypeCategoryId = parentAccountTypeCategoryId;
    }

    public int getDebitOrCredit() {
        return debitOrCredit;
    }

    public void setDebitOrCredit(int debitOrCredit) {
        this.debitOrCredit = debitOrCredit;
    }
}
