package com.example.likeqianwang.DataModel;

import java.util.List;

public class AccountTypeCategory {
    private String name;
    private List<AccountTypeItem> items;
    private boolean isExpanded;
    private String id; // 用于记忆状态

    public AccountTypeCategory(String name, List<AccountTypeItem> items, String id) {
        this.name = name;
        this.items = items;
        this.isExpanded = true;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AccountTypeItem> getItems() {
        return items;
    }

    public void setItems(List<AccountTypeItem> items) {
        this.items = items;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
