package com.example.likeqianwang.ui.recording_date_time_picker;

import android.app.Dialog;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.DatePicker;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.likeqianwang.R;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.Calendar;

public class RecordingDateTimePickerDialog extends BottomSheetDialogFragment {
    private DateTimePickerListener listener;
    private Calendar selectedDateTime;

    public interface DateTimePickerListener {
        void onDateTimeSelected(Calendar dateTime);
    }

    public static RecordingDateTimePickerDialog newInstance() {
        return new RecordingDateTimePickerDialog();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        try {
            listener = (DateTimePickerListener) context;
        } catch (ClassCastException e) {
            throw new ClassCastException(context + " 必须实现 DateTimePickerListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置自定义样式
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);

        // 设置对话框背景半透明
        setDialogBackgroundDim();
    }

    // 设置对话框背景半透明
    private void setDialogBackgroundDim() {
        // 使用主题设置
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setDimAmount(0.5f); // 设置背景暗度（0-1，0为全透明，1为全黑）
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置对话框显示时为展开状态
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置圆角背景
                setupBottomSheetBackground(bottomSheet);
            }
        });

        return dialog;
    }

    // 设置底部表单的圆角背景
    private void setupBottomSheetBackground(View bottomSheet) {
        // 创建圆角形状
        ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel.Builder()
                .setTopRightCorner(CornerFamily.ROUNDED, dpToPx(16))
                .setTopLeftCorner(CornerFamily.ROUNDED, dpToPx(16))
                .build();

        // 创建MaterialShapeDrawable
        MaterialShapeDrawable shapeDrawable = new MaterialShapeDrawable(shapeAppearanceModel);
        shapeDrawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        shapeDrawable.setElevation(dpToPx(8));
        shapeDrawable.setShadowColor(Color.LTGRAY);

        // 设置背景
        bottomSheet.setBackground(shapeDrawable);
    }

    private float dpToPx(int dp) {
        return dp * getResources().getDisplayMetrics().density;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_date_time_picker_view, container, false);

        // 初始化当前日期时间
        selectedDateTime = Calendar.getInstance();

        // 获取控件引用
        DatePicker datePicker = view.findViewById(R.id.recording_page_DatePicker);
        TimePicker timePicker = view.findViewById(R.id.recording_page_TimePicker);
        TextView confirmButton = view.findViewById(R.id.DateTimePicker_confirm);
        TextView cancelButton = view.findViewById(R.id.DateTimePicker_cancel);

        // 设置日期选择器初始值
        datePicker.init(
                selectedDateTime.get(Calendar.YEAR),
                selectedDateTime.get(Calendar.MONTH),
                selectedDateTime.get(Calendar.DAY_OF_MONTH),
                (view1, year, monthOfYear, dayOfMonth) -> {
                    selectedDateTime.set(Calendar.YEAR, year);
                    selectedDateTime.set(Calendar.MONTH, monthOfYear);
                    selectedDateTime.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                }
        );

        // 设置时间选择器初始值和监听器
        timePicker.setIs24HourView(true);
        timePicker.setHour(selectedDateTime.get(Calendar.HOUR_OF_DAY));
        timePicker.setMinute(selectedDateTime.get(Calendar.MINUTE));
        timePicker.setOnTimeChangedListener((view12, hourOfDay, minute) -> {
            selectedDateTime.set(Calendar.HOUR_OF_DAY, hourOfDay);
            selectedDateTime.set(Calendar.MINUTE, minute);
        });

        // 设置确认按钮点击事件
        confirmButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDateTimeSelected(selectedDateTime);
            }
            dismiss();
        });

        // 设置取消按钮点击事件
        cancelButton.setOnClickListener(v -> dismiss());

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 设置对话框的样式，使其占满屏幕宽度
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
        }

        // 设置背景半透明
        View parent = (View) view.getParent();
        parent.setBackgroundColor(Color.TRANSPARENT);
    }
}
