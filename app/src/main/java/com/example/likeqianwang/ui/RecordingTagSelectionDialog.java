package com.example.likeqianwang.ui;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.example.likeqianwang.TagManagementActivity;
import com.example.likeqianwang.ViewModel.TransactionTagViewModel;
import com.example.likeqianwang.adapters.RecordingTagCategory_adapter;
import com.google.android.flexbox.FlexboxLayout;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RecordingTagSelectionDialog extends BottomSheetDialog {
    private final Context context;
    private TransactionTagViewModel viewModel;
    private RecordingTagCategory_adapter adapter;
    private OnTagSelectionListener listener;

    // UI组件
    private LinearLayout llSelectedTagsContainer;
    private FlexboxLayout flexboxSelectedTags;
    private RecyclerView rvTagCategories;
    private TextView btnCancel;
    private TextView btnConfirm;
    private TextView tvTagManagement;

    private List<TransactionTag> selectedTags = new ArrayList<>();

    public RecordingTagSelectionDialog(Context context, ViewModelStoreOwner owner) {
        super(context);
        this.context = context;
        initViewModel(owner);
        initView();
        setupObservers();
    }

    private void initViewModel(ViewModelStoreOwner owner) {
        viewModel = new ViewModelProvider(owner).get(TransactionTagViewModel.class);
    }

    private void initView() {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_recording_tag_selection_view, null);
        setContentView(view);

        llSelectedTagsContainer = view.findViewById(R.id.TagSelection_container);
        flexboxSelectedTags = view.findViewById(R.id.flexbox_selected_tags);
        rvTagCategories = view.findViewById(R.id.rv_TagSelection_categories);
        btnCancel = view.findViewById(R.id.TagSelection_cancel);
        btnConfirm = view.findViewById(R.id.TagSelection_confirm);
        tvTagManagement = view.findViewById(R.id.tv_TagSelection_management);

        // 设置RecyclerView
        rvTagCategories.setLayoutManager(new LinearLayoutManager(context));

        // 设置点击事件
        btnCancel.setOnClickListener(v -> dismiss());
        btnConfirm.setOnClickListener(v -> {
            if (listener != null) {
                List<TransactionTag> currentSelectedTags = viewModel.getSelectedTags().getValue();
                if (currentSelectedTags != null && !currentSelectedTags.isEmpty()) {
                    // 添加调试日志
                    Log.d("RecordingTagSelectionDialog", "Confirming selection from ViewModel. Selected tags count: " + currentSelectedTags.size());
                    for (TransactionTag tag : currentSelectedTags) {
                        Log.d("RecordingTagSelectionDialog", "Selected tag: " + tag.getTagName() + ", ID: " + tag.getTagId());
                    }

                    // 使用ViewModel中的选中标签
                    listener.onTagsSelected(new ArrayList<>(currentSelectedTags));
                } else {
                    // 如果ViewModel中没有选中的标签，使用本地的selectedTags
                    Log.d("RecordingTagSelectionDialog", "Confirming selection from local. Selected tags count: " + selectedTags.size());
                    listener.onTagsSelected(new ArrayList<>(selectedTags));
                }
            }
            dismiss();
        });

        tvTagManagement.setOnClickListener(v -> {
            Intent intent = new Intent(context, TagManagementActivity.class);
            context.startActivity(intent);
        });
    }

    private void setupObservers() {
        // 观察分类化的标签数据
        viewModel.getCategorizedTags().observe((LifecycleOwner) context, categorizedTags -> {
            if (categorizedTags != null) {
                updateTagCategories(categorizedTags);
            }
        });

        // 观察选中的标签
        viewModel.getSelectedTags().observe((LifecycleOwner) context, tags -> {
            if (tags != null) {
                // 添加调试日志
                Log.d("RecordingTagSelectionDialog", "ViewModel selected tags updated. Count: " + tags.size());

                selectedTags.clear();
                selectedTags.addAll(tags);
                updateSelectedTagsDisplay();
                if (adapter != null) {
                    // 创建新的列表传递给适配器
                    List<TransactionTag> adapterTags = new ArrayList<>(selectedTags);
                    adapter.updateSelectedTags(adapterTags);
                    adapter.notifyDataSetChanged(); // 强制刷新适配器
                }
            }
        });
    }

    private void updateTagCategories(Map<String, List<TransactionTag>> categorizedTags) {
        List<String> categories = new ArrayList<>(categorizedTags.keySet());

        if (adapter == null) {
            adapter = new RecordingTagCategory_adapter(context, categories, categorizedTags, selectedTags);
            adapter.setOnTagClickListener(tag -> {
                // 添加调试日志
                Log.d("RecordingTagSelectionDialog", "Tag clicked: " + tag.getTagName() + ", ID: " + tag.getTagId());

                viewModel.toggleTagSelection(tag);
            });
            rvTagCategories.setAdapter(adapter);
        } else {
            // 创建新的列表传递给适配器
            List<TransactionTag> adapterTags = new ArrayList<>(selectedTags);
            adapter.updateSelectedTags(adapterTags);
            adapter.notifyDataSetChanged(); // 强制刷新适配器
        }
    }

    private void updateSelectedTagsDisplay() {
        if (selectedTags.isEmpty()) {
            llSelectedTagsContainer.setVisibility(View.GONE);
        } else {
            llSelectedTagsContainer.setVisibility(View.VISIBLE);
            flexboxSelectedTags.removeAllViews();

            for (TransactionTag tag : selectedTags) {
                View tagView = createSelectedTagView(tag);
                flexboxSelectedTags.addView(tagView);
            }
        }
    }

    private View createSelectedTagView(TransactionTag tag) {
        TextView tagView = new TextView(context);
        tagView.setText(tag.getTagName());
        tagView.setTextSize(12);
        tagView.setTextColor(Color.WHITE);
        tagView.setBackgroundResource(R.drawable.widget_tag_item_bg);
        tagView.setBackgroundTintList(context.getColorStateList(R.color.ChaHuaHong));
        tagView.setPadding(16, 8, 16, 8);

        FlexboxLayout.LayoutParams params = new FlexboxLayout.LayoutParams(
                FlexboxLayout.LayoutParams.WRAP_CONTENT,
                FlexboxLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(8, 4, 8, 4);
        tagView.setLayoutParams(params);

        return tagView;
    }

    public void setSelectedTags(List<TransactionTag> tags) {
        if (tags != null) {
            // 添加调试日志
            Log.d("RecordingTagSelectionDialog", "Setting selected tags. Count: " + tags.size());

            // 清除当前选择
            selectedTags.clear();
            // 添加传入的标签
            selectedTags.addAll(tags);
            // 更新UI显示
            updateSelectedTagsDisplay();
            // 如果适配器已初始化，更新适配器中的选择状态
            if (adapter != null) {
                // 创建新的列表传递给适配器
                List<TransactionTag> adapterTags = new ArrayList<>(selectedTags);
                adapter.updateSelectedTags(adapterTags);
                adapter.notifyDataSetChanged(); // 强制刷新适配器
            }
        }
    }

    // 确保在对话框显示时正确显示已选择的标签
    @Override
    public void show() {
        super.show();
        // 确保在显示对话框时更新已选择标签的显示
        updateSelectedTagsDisplay();
        // 如果适配器已初始化，确保更新适配器中的选择状态
        if (adapter != null) {
            // 创建新的列表传递给适配器
            List<TransactionTag> adapterTags = new ArrayList<>(selectedTags);
            adapter.updateSelectedTags(adapterTags);
            adapter.notifyDataSetChanged(); // 强制刷新适配器
        }
    }

    public void setOnTagSelectionListener(OnTagSelectionListener listener) {
        this.listener = listener;
    }

    public interface OnTagSelectionListener {
        void onTagsSelected(List<TransactionTag> selectedTags);
    }

}
