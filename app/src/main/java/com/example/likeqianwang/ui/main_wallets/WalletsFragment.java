package com.example.likeqianwang.ui.main_wallets;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountCategory;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Repository.AccountRepository;
import com.example.likeqianwang.Utils.CurrencyFormatter;
import com.example.likeqianwang.Utils.SwipeToDeleteCallback;
import com.example.likeqianwang.adapters.AccountCategoryInWallet_adapter;
import com.example.likeqianwang.adapters.AccountItemInWallet_adapter;
import com.example.likeqianwang.databinding.FragmentWalletBinding;
import com.example.likeqianwang.ui.wallets_add_new_account.WalletsAddNewAccountDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WalletsFragment extends Fragment implements 
    AccountCategoryInWallet_adapter.OnAccountClickListener, 
    AccountCategoryInWallet_adapter.NestedAdapterProvider,
    AccountCategoryInWallet_adapter.NestedRecyclerViewProvider,
    AccountCategoryInWallet_adapter.OnAccountDeleteListener {

    private FragmentWalletBinding binding;
    private WalletsViewModel walletsViewModel;
    private AccountCategoryInWallet_adapter categoryAdapter;
    private List<AccountCategory> categories;
    private AccountRepository accountRepository;
    private SwipeToDeleteCallback swipeHandler;
    // 添加成员变量来跟踪当前滑动的ViewHolder
    private RecyclerView.ViewHolder currentViewHolder;
    // 存储嵌套适配器的引用
    private Map<Integer, RecyclerView.Adapter<?>> nestedAdapters = new HashMap<>();
    // 新增：存储嵌套RecyclerView的引用
    private Map<Integer, RecyclerView> nestedRecyclerViews = new HashMap<>();

    // 添加 ActivityResultLauncher 用于启动 FillNewAccountInfoActivity
    private final ActivityResultLauncher<Intent> addAccountLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == android.app.Activity.RESULT_OK) {
                    // 当返回结果为 RESULT_OK 时，刷新账户列表
                    loadAccounts();
                }
            });

    // 实现接口方法
    @Override
    public RecyclerView.Adapter<?> getNestedAdapter(int categoryPosition) {
        return nestedAdapters.get(categoryPosition);
    }
    
    // 实现新的接口方法
    @Override
    public RecyclerView getNestedRecyclerView(int categoryPosition) {
        return nestedRecyclerViews.get(categoryPosition);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 在这里初始化 accountRepository，确保在使用前已经创建
        accountRepository = new AccountRepository(requireActivity().getApplication());

        // 初始化 ViewModel
        walletsViewModel = new ViewModelProvider(this).get(WalletsViewModel.class);

        // 初始化 categories 列表
        if (categories == null) {
            categories = new ArrayList<>();
        }

        // 添加默认分类
        if (categories.isEmpty()) {
            // 手动添加已知的分类
            categories.add(new AccountCategory("cat_cash", "现金账户"));
            categories.add(new AccountCategory("cat_bank", "银行账户"));
            categories.add(new AccountCategory("cat_digital", "数字账户"));

            // 设置默认展开
            for (AccountCategory category : categories) {
                category.setExpanded(true);
            }
        }

        // 预加载数据 - 在Fragment创建时就开始加载数据
        preloadData();

        // 观察账户数据变化
        walletsViewModel.getAllAccounts().observe(this, accounts -> {
            if (accounts != null) {
                updateUI(accounts);
            }
        });
    }

    // 新增预加载数据方法
    private void preloadData() {
        // 使用ViewModel预加载数据
        walletsViewModel.preloadAccounts();

        // 使用后台线程加载账户数据
        new Thread(() -> {
            try {
                List<Account> accounts = accountRepository.getAllAccountsSync();
                if (accounts != null && !accounts.isEmpty()) {
                    // 在主线程中更新UI
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            preloadAccountsToCategories(accounts);
                            // 如果视图已创建，则更新UI
                            if (binding != null) {
                                updateUI(accounts);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                Log.e("WalletsFragment", "后台线程加载账户数据失败", e);
            }
        }).start();
    }

    // 新增方法，用于预加载账户到分类
    private void preloadAccountsToCategories(List<Account> accounts) {
        // 将账户分配到对应分类
        Map<String, AccountCategory> categoryMap = new HashMap<>();
        for (AccountCategory category : categories) {
            categoryMap.put(category.getId(), category);
            // 确保每次加载前清空账户列表
            category.setAccounts(new ArrayList<>());
            category.setTotalAmount(0.0);
        }

        // 将账户分配到对应分类
        for (Account account : accounts) {
            String categoryId = account.getAccountTypeCategoryId();
            if (categoryId == null || categoryId.isEmpty()) {
                continue;
            }

            AccountCategory category = categoryMap.get(categoryId);
            if (category != null) {
                category.addAccount(account);

                // 更新分类总金额
                if (account.isIncludeInAsset()) {
                    double amountChange = account.getAccountTypeDebitCredit() == 1 ?
                            account.getAccountBalance() : -account.getAccountBalance();
                    category.setTotalAmount(category.getTotalAmount() + amountChange);
                }
            }
        }
    }

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        walletsViewModel = new ViewModelProvider(this).get(WalletsViewModel.class);

        binding = FragmentWalletBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        // 确保 accountRepository 已初始化
        if (accountRepository == null) {
            accountRepository = new AccountRepository(requireActivity().getApplication());
        }

        // 设置RecyclerView
        RecyclerView categoryList = binding.walletsAccountCategoryList;
        categoryList.setLayoutManager(new LinearLayoutManager(requireContext()));

        // 初始化适配器
        categoryAdapter = new AccountCategoryInWallet_adapter(requireContext(), categories, this);
        categoryAdapter.setOnAccountDeleteListener(this); // 设置删除监听器

        binding.walletsAccountCategoryList.setAdapter(categoryAdapter);

        binding.walletsAddNewAccountButton.setOnClickListener(v -> addNewAccount());

        // 立即显示加载中状态
        binding.walletsAccountCategoryList.setVisibility(View.VISIBLE);

        // 加载账户数据
        loadAccounts();

        // 立即更新资产摘要
        updateAssetSummary();

        return root;
    }

    // 新增更新UI方法，整合了多个UI更新操作
    private void updateUI(List<Account> accounts) {
        // 提取分类
        extractCategoriesFromAccounts(accounts);

        // 更新适配器
        if (categoryAdapter != null) {
            // 保存当前分类的展开状态
            Map<String, Boolean> expansionStates = new HashMap<>();
            for (AccountCategory category : categories) {
                expansionStates.put(category.getId(), category.isExpanded());
            }

            // 更新分类列表
            List<AccountCategory> updatedCategories = createCategoriesFromAccounts(accounts);

            // 过滤掉没有账户的分类，但保留分类的基本信息
            List<AccountCategory> visibleCategories = new ArrayList<>();
            for (AccountCategory category : updatedCategories) {
                if (category.getAccounts() != null && !category.getAccounts().isEmpty()) {
                    // 恢复展开状态
                    Boolean wasExpanded = expansionStates.get(category.getId());
                    if (wasExpanded != null) {
                        category.setExpanded(wasExpanded);
                    } else {
                        // 新分类默认展开
                        category.setExpanded(true);
                    }
                    visibleCategories.add(category);
                }
            }

            // 如果没有可见分类，显示空状态
            if (visibleCategories.isEmpty()) {
                binding.walletsAccountCategoryList.setVisibility(View.GONE);
                updateEmptyAssetSummary();
            } else {
                binding.walletsAccountCategoryList.setVisibility(View.VISIBLE);
                // 更新适配器数据
                categories.clear();
                categories.addAll(visibleCategories);
                categoryAdapter.notifyDataSetChanged();
            }
        }
    }

    // 新增方法：从账户列表创建分类列表
    private List<AccountCategory> createCategoriesFromAccounts(List<Account> accounts) {
        Map<String, AccountCategory> categoryMap = new HashMap<>();

        // 先添加默认分类
        categoryMap.put("cat_cash", new AccountCategory("cat_cash", "现金账户"));
        categoryMap.put("cat_bank", new AccountCategory("cat_bank", "银行账户"));
        categoryMap.put("cat_digital", new AccountCategory("cat_digital", "数字账户"));

        // 将账户分配到对应分类
        for (Account account : accounts) {
            String categoryId = account.getAccountTypeCategoryId();
            if (categoryId == null || categoryId.isEmpty()) {
                continue;
            }

            // 获取或创建分类
            AccountCategory category = categoryMap.get(categoryId);
            if (category == null) {
                category = new AccountCategory(categoryId, getCategoryNameById(categoryId));
                categoryMap.put(categoryId, category);
            }

            // 添加账户到分类
            category.addAccount(account);

            // 更新分类总金额
            if (account.isIncludeInAsset()) {
                double amountChange = account.getAccountTypeDebitCredit() == 1 ?
                        account.getAccountBalance() : -account.getAccountBalance();
                category.setTotalAmount(category.getTotalAmount() + amountChange);
            }
        }

        // 转换为列表并返回
        return new ArrayList<>(categoryMap.values());
    }

    // 辅助方法：根据分类ID获取分类名称
    private String getCategoryNameById(String categoryId) {
        return switch (categoryId) {
            case "cat_cash" -> "现金账户";
            case "cat_bank" -> "银行账户";
            case "cat_digital" -> "数字账户";
            default -> "其他账户";
        };
    }

    private void loadAccounts() {
        // 从数据库加载账户数据
        walletsViewModel.getAllAccounts().observe(getViewLifecycleOwner(), accounts -> {
            if (accounts != null) {
                // 更新UI
                updateUI(accounts);

                // 更新资产摘要
                updateAssetSummary();
            }
        });
    }

    // 添加一个新方法，用于在没有账户时更新资产摘要
    private void updateEmptyAssetSummary() {
        binding.tvWidgetAssetLiabilityTotalAssetBalance.setText(CurrencyFormatter.format(0));
        binding.tvWidgetAssetLiabilityTotalLiabilityBalance.setText(CurrencyFormatter.format(0));
        binding.tvWidgetAssetLiabilityNetAssetBalance.setText(CurrencyFormatter.format(0));
    }

    private void extractCategoriesFromAccounts(List<Account> accounts) {
        // 创建当前分类的深拷贝作为旧分类列表
        List<AccountCategory> oldCategories = new ArrayList<>(categories.size());
        for (AccountCategory category : categories) {
            AccountCategory oldCategory = new AccountCategory(category.getId(), category.getName());
            oldCategory.setTotalAmount(category.getTotalAmount());
            oldCategory.setExpanded(category.isExpanded());
            oldCategory.setAccounts(new ArrayList<>(category.getAccounts()));
            oldCategories.add(oldCategory);
        }

        // 清空现有分类中的账户列表
        for (AccountCategory category : categories) {
            category.setAccounts(new ArrayList<>());
            category.setTotalAmount(0.0);
        }

        // 创建分类ID到分类对象的映射，方便查找
        Map<String, AccountCategory> categoryMap = new HashMap<>(categories.size());
        for (AccountCategory category : categories) {
            categoryMap.put(category.getId(), category);
        }

        // 将账户分配到对应分类
        for (Account account : accounts) {
            String categoryId = account.getAccountTypeCategoryId();
            if (categoryId == null || categoryId.isEmpty()) {
                continue;
            }

            AccountCategory category = categoryMap.get(categoryId);
            if (category != null) {
                // 添加账户到分类
                category.addAccount(account);

                // 更新分类总金额
                if (account.isIncludeInAsset()) {
                    double amountChange = account.getAccountTypeDebitCredit() == 1 ?
                            account.getAccountBalance() : -account.getAccountBalance();
                    category.setTotalAmount(category.getTotalAmount() + amountChange);
                }
            }
        }

        // 检查是否有账户
        boolean hasAccounts = false;
        for (AccountCategory category : categories) {
            if (category.getAccounts() != null && !category.getAccounts().isEmpty()) {
                hasAccounts = true;
                break;
            }
        }

        // 如果没有账户，显示空状态
        if (!hasAccounts) {
            binding.walletsAccountCategoryList.setVisibility(View.GONE);
            updateEmptyAssetSummary();
        } else {
            binding.walletsAccountCategoryList.setVisibility(View.VISIBLE);
        }

        // 使用 DiffUtil 更新适配器
        if (categoryAdapter != null) {
            updateCategoriesWithDiffUtil(oldCategories, categories);
        }

        // 检查每个分类是否有账户，如果没有则隐藏
        updateCategoryVisibility();

        // 通知适配器更新
        if (categoryAdapter != null) {
            categoryAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 使用 DiffUtil 更新分类列表
     *
     * @param oldCategories 旧的分类列表
     * @param newCategories 新的分类列表
     */
    private void updateCategoriesWithDiffUtil(List<AccountCategory> oldCategories, List<AccountCategory> newCategories) {
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return oldCategories.size();
            }

            @Override
            public int getNewListSize() {
                return newCategories.size();
            }

            @Override
            public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
                return oldCategories.get(oldItemPosition).getId().equals(
                        newCategories.get(newItemPosition).getId());
            }

            @Override
            public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
                AccountCategory oldCategory = oldCategories.get(oldItemPosition);
                AccountCategory newCategory = newCategories.get(newItemPosition);

                // 比较基本信息
                if (!oldCategory.getName().equals(newCategory.getName()) ||
                        oldCategory.getTotalAmount() != newCategory.getTotalAmount() ||
                        oldCategory.isExpanded() != newCategory.isExpanded()) {
                    return false;
                }

                // 比较账户列表
                List<Account> oldAccounts = oldCategory.getAccounts();
                List<Account> newAccounts = newCategory.getAccounts();

                if (oldAccounts.size() != newAccounts.size()) {
                    return false;
                }

                // 简单比较账户ID列表是否相同
                for (int i = 0; i < oldAccounts.size(); i++) {
                    if (!oldAccounts.get(i).getAccountId().equals(newAccounts.get(i).getAccountId())) {
                        return false;
                    }
                }

                return true;
            }
        });

        // 将计算出的差异应用到适配器
        diffResult.dispatchUpdatesTo(categoryAdapter);
    }

    /**
     * 更新分类的可见性，隐藏没有账户的分类
     */
    private void updateCategoryVisibility() {
        // 创建一个新的分类列表，只包含有账户的分类
        List<AccountCategory> visibleCategories = new ArrayList<>();

        for (AccountCategory category : categories) {
            if (category.getAccounts() != null && !category.getAccounts().isEmpty()) {
                visibleCategories.add(category);
            }
        }

        // 如果所有分类都没有账户，显示空状态
        if (visibleCategories.isEmpty()) {
            binding.walletsAccountCategoryList.setVisibility(View.GONE);
        } else {
            // 否则更新适配器，只显示有账户的分类
            if (categoryAdapter != null) {
                categoryAdapter.updateCategories(visibleCategories);
            }
            binding.walletsAccountCategoryList.setVisibility(View.VISIBLE);
        }
    }

    private void updateAssetSummary() {
        // 使用 LiveData 观察账户变化
        walletsViewModel.getAllAccounts().observe(getViewLifecycleOwner(), accounts -> {
            if (accounts == null || accounts.isEmpty()) {
                updateEmptyAssetSummary();
                return;
            }

            double totalAssets = 0;
            double totalLiabilities = 0;

            for (Account account : accounts) {
                if (account.isIncludeInAsset()) {
                    if (account.getAccountTypeDebitCredit() == 1) {
                        // 借记卡/储蓄卡，余额为正资产
                        totalAssets += account.getAccountBalance();
                    } else {
                        // 信用卡，欠款为负债
                        totalLiabilities += account.getAccountBalance();
                    }
                }
            }

            // 计算净资产
            double netWorth = totalAssets - totalLiabilities;

            // 更新 UI
            binding.tvWidgetAssetLiabilityTotalAssetBalance.setText(CurrencyFormatter.format(totalAssets));
            binding.tvWidgetAssetLiabilityTotalLiabilityBalance.setText(CurrencyFormatter.format(totalLiabilities));
            binding.tvWidgetAssetLiabilityNetAssetBalance.setText(CurrencyFormatter.format(netWorth));
        });
    }

    private void addNewAccount() {
        WalletsAddNewAccountDialog dialog = WalletsAddNewAccountDialog.newInstance();
        dialog.show(getChildFragmentManager(), "add_account_dialog");
    }

    // 当 WalletsAddNewAccountDialog 调用 startActivity 时，使用我们的 launcher
    public void startAccountInfoActivity(Intent intent) {
        addAccountLauncher.launch(intent);
    }

    @Override
    public void onAccountClick(String accountId) {
        // 处理账户点击事件
        // 这里可以跳转到账户详情页面
        // Intent intent = new Intent(getContext(), AccountDetailActivity.class);
        // intent.putExtra("ACCOUNT_ID", accountId);
        // startActivity(intent);
    }

    // 实现OnAccountDeleteListener接口方法
    @Override
    public void onAccountDelete(Account account) {
        // 显示确认对话框
        new AlertDialog.Builder(requireContext())
                .setTitle("确认删除")
                .setMessage("您确定要删除账户 " + account.getAccountName() + " 吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    // 执行删除操作
                    performAccountDelete(account);
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    // 取消删除，关闭滑动项
                    closeSwipedItem(account);
                })
                .setOnCancelListener(dialog -> {
                    // 对话框取消时也关闭滑动项
                    closeSwipedItem(account);
                })
                .show();
    }

    // 执行账户删除操作
    private void performAccountDelete(Account account) {
        // 从数据库中删除账户
        accountRepository.deleteAccount(account, success -> {
            if (success) {
                // 删除成功，刷新UI
                loadAccounts();
                Toast.makeText(requireContext(), "账户已删除", Toast.LENGTH_SHORT).show();
            } else {
                // 删除失败
                Toast.makeText(requireContext(), "删除失败，请重试", Toast.LENGTH_SHORT).show();
                // 关闭滑动项
                closeSwipedItem(account);
            }
        });
    }

    // 关闭滑动项
    private void closeSwipedItem(Account account) {
        // 找到账户所在的分类和位置
        for (int i = 0; i < categories.size(); i++) {
            AccountCategory category = categories.get(i);
            List<Account> accounts = category.getAccounts();
            if (accounts != null) {
                for (int j = 0; j < accounts.size(); j++) {
                    if (accounts.get(j).getAccountId().equals(account.getAccountId())) {
                        // 找到账户，关闭滑动项
                        RecyclerView nestedRv = getNestedRecyclerView(i);
                        if (nestedRv != null) {
                            RecyclerView.Adapter<?> adapter = nestedRv.getAdapter();
                            if (adapter instanceof AccountItemInWallet_adapter) {
                                AccountItemInWallet_adapter accountAdapter = (AccountItemInWallet_adapter) adapter;
                                accountAdapter.closeItem(j, nestedRv);
                            }
                        }
                        return;
                    }
                }
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 在Fragment恢复时刷新数据，确保显示最新状态
        loadAccounts();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}