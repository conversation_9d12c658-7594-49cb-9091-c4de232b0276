package com.example.likeqianwang.ui.recording_transer;

import android.content.res.ColorStateList;
import android.graphics.Outline;
import android.graphics.Path;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.ItemRecordingTransferViewBinding;
import com.example.likeqianwang.interfaces.RecordingDataProvider;
import com.example.likeqianwang.ui.RecordingTransferAccountSelectionDialog;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class RecordingTransferFragment extends Fragment implements RecordingDataProvider {
    private ItemRecordingTransferViewBinding recordingTransferViewBinding;

    // 账户相关变量
    private Account fromAccount;
    private Account toAccount;
    private String transferType = "转账"; // 默认为普通转账

    // 账户选择对话框类型标识
    private static final int FROM_ACCOUNT = 1;
    private static final int TO_ACCOUNT = 2;
    private int currentSelectionType;

    // 数据库访问
    private AccountDao accountDao;
    private List<Account> allAccounts;
    private ExecutorService executorService;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        recordingTransferViewBinding = ItemRecordingTransferViewBinding.inflate(inflater, container, false);
        return recordingTransferViewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 初始化数据库访问
        accountDao = AppDatabase.getInstance(requireContext()).accountDao();
        executorService = Executors.newSingleThreadExecutor();

        // 加载所有账户
        loadAccounts();

        // 获取圆角半径
        float cornerRadius = getResources().getDimensionPixelSize(R.dimen.recording_transfer_account_icon_circle_radius);

        // 为转出账户的 ConstraintLayout (顶部圆角)
        ViewOutlineProvider topCornersOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                Path path = new Path();
                // 定义顶部圆角，底部直角的路径
                // 左上角，右上角，右下角，左下角
                path.addRoundRect(
                        0f,
                        0f,
                        view.getWidth(),
                        view.getHeight(),
                        new float[]{cornerRadius, cornerRadius, cornerRadius, cornerRadius, 0, 0, 0, 0},
                        Path.Direction.CW
                );
                outline.setPath(path);
            }
        };
        recordingTransferViewBinding.recordingPageTransferFromAccount.setOutlineProvider(topCornersOutlineProvider);
        recordingTransferViewBinding.recordingPageTransferFromAccount.setClipToOutline(true);

        // 为转入账户的 ConstraintLayout (底部圆角)
        ViewOutlineProvider bottomCornersOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                Path path = new Path();
                // 定义底部圆角，顶部直角的路径
                // 左上角，右上角，右下角，左下角
                path.addRoundRect(
                        0f,
                        0f,
                        view.getWidth(),
                        view.getHeight(),
                        new float[]{0, 0, 0, 0, cornerRadius, cornerRadius, cornerRadius, cornerRadius},
                        Path.Direction.CW
                );
                outline.setPath(path);
            }
        };
        recordingTransferViewBinding.recordingPageTransferToAccount.setOutlineProvider(bottomCornersOutlineProvider);
        recordingTransferViewBinding.recordingPageTransferToAccount.setClipToOutline(true);

        // 设置转出账户按钮点击事件
        recordingTransferViewBinding.recordingPageTransferFromAccount.setOnClickListener(v -> {
            currentSelectionType = FROM_ACCOUNT;
            showAccountSelectionDialog("选择转出账户");
        });

        // 设置转入账户按钮点击事件
        recordingTransferViewBinding.recordingPageTransferToAccount.setOnClickListener(v -> {
            currentSelectionType = TO_ACCOUNT;
            showAccountSelectionDialog("选择转入账户");
        });

        // 设置交换按钮点击事件
        recordingTransferViewBinding.btnRecordingPageTransferSwapAccounts.setOnClickListener(v -> swapAccounts());
    }

    /**
     * 加载所有账户数据
     */
    private void loadAccounts() {
        executorService.execute(() -> {
            allAccounts = accountDao.getAllAccountsSync();
            // 确保在主线程更新UI
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    // 账户加载完成后的操作（如果需要）
                });
            }
        });
    }

    /**
     * 显示账户选择底部对话框
     */
    private void showAccountSelectionDialog(String title) {
        if (allAccounts == null || allAccounts.isEmpty()) {
            Toast.makeText(requireContext(), "没有可用账户", Toast.LENGTH_SHORT).show();
            return;
        }

        Account currentAccount = (currentSelectionType == FROM_ACCOUNT) ? toAccount : fromAccount;

        RecordingTransferAccountSelectionDialog transferAccountSelectionDialog = RecordingTransferAccountSelectionDialog.newInstance(
                title,
                allAccounts,
                account -> {
                    if (account.equals(currentAccount)) { // 如果是当前选中的账户，则不触发点击事件
                        Toast.makeText(requireContext(), "选择的账户与当前账户重复", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (currentSelectionType == FROM_ACCOUNT) {
                        setFromAccount(account);
                    } else {
                        setToAccount(account);
                    }
                    updateTransferType();
                }
        );
        transferAccountSelectionDialog.show(getChildFragmentManager(), "AccountSelection");
    }

    /**
     * 设置转出账户
     */
    private void setFromAccount(Account account) {
        fromAccount = account;

        // 更新转出账户按钮显示
        if (account != null) {
            // 创建并设置圆形图标
            ShapeableImageView iconView = recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon;
            iconView.setVisibility(View.VISIBLE);
            if (account.getBankIcon() != 0) {
                iconView.setImageResource(account.getBankIcon());
            } else {
                iconView.setImageResource(account.getAccountTypeIcon());
            }

            // 设置圆形样式
            ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel()
                    .toBuilder()
                    .setAllCorners(CornerFamily.ROUNDED, getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius))
                    .build();
            iconView.setShapeAppearanceModel(shapeAppearanceModel);

            // 设置边框
            iconView.setStrokeWidth(1);
            iconView.setStrokeColor(ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.YinBai)));

            // 设置图标内边距
            int padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_icon_padding);

            // 应用内边距
            iconView.setPadding(padding, padding, padding, padding);

            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setText(account.getAccountName());
            int name_padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_name_padding_start);
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setPadding(name_padding,0,0,0);
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setText(String.format(Locale.CHINA, "%,.2f", account.getAccountBalance()));
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setVisibility(View.VISIBLE);
        } else {
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setText(R.string.recording_transfer_select_transfer_out_account); // 或者其他默认文本
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setImageDrawable(null); // 清除图标
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setText(""); // 清除余额
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setVisibility(View.GONE);
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setVisibility(View.GONE);
        }
    }

    /**
     * 设置转入账户
     */
    private void setToAccount(Account account) {
        toAccount = account;

        // 更新转入账户按钮显示
        if (account != null) {
            // 创建并设置圆形图标
            ShapeableImageView iconView = recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon;
            iconView.setVisibility(View.VISIBLE);
            if (account.getBankIcon() != 0) {
                iconView.setImageResource(account.getBankIcon());
            } else {
                iconView.setImageResource(account.getAccountTypeIcon());
            }

            // 设置圆形样式
            ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel()
                    .toBuilder()
                    .setAllCorners(CornerFamily.ROUNDED, getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius))
                    .build();
            iconView.setShapeAppearanceModel(shapeAppearanceModel);

            // 设置边框
            iconView.setStrokeWidth(1);
            iconView.setStrokeColor(ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.YinBai)));

            // 设置图标内边距
            int padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_icon_padding);

            // 应用内边距
            iconView.setPadding(padding, padding, padding, padding);

            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setText(account.getAccountName());
            int name_padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_name_padding_start);
            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setPadding(name_padding,0,0,0);
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setText(String.format(Locale.CHINA, "%,.2f", account.getAccountBalance()));
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setVisibility(View.VISIBLE);
        } else {
            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setText(R.string.recording_transfer_select_transfer_out_account); // 或者其他默认文本
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setImageDrawable(null); // 清除图标
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setText(""); // 清除余额
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setVisibility(View.GONE);
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setVisibility(View.GONE);
        }
    }

    /**
     * 交换转入转出账户
     */
    private void swapAccounts() {
        // 临时保存账户
        Account tempFromAccount = fromAccount;
        Account tempToAccount = toAccount;

        // 重置账户引用
        fromAccount = null;
        toAccount = null;

        // 交换账户
        if (tempFromAccount != null) {
            setToAccount(tempFromAccount);
        } else {
            // 如果转出账户为空，保持转入按钮原样
            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setText(R.string.recording_transfer_select_transfer_in_account); // 或者其他默认文本
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setImageDrawable(null); // 清除图标
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setText(""); // 清除余额
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setVisibility(View.GONE);
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setVisibility(View.GONE);
        }

        if (tempToAccount != null) {
            setFromAccount(tempToAccount);
        } else {
            // 如果转入账户为空，保持转出按钮原样
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setText(R.string.recording_transfer_select_transfer_out_account); // 或者其他默认文本
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setImageDrawable(null); // 清除图标
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setText(""); // 清除余额
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setVisibility(View.GONE);
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setVisibility(View.GONE);
        }

        // 更新转账类型
        updateTransferType();
    }

    /**
     * 根据账户类型更新转账类型
     * - 当转出账户为信用卡或转入账户为现金时，设为"提现"
     * - 当转入账户为信用卡时，设为"还款"
     * - 其他情况为普通"转账"
     */
    private void updateTransferType() {
        if (fromAccount == null || toAccount == null) {
            return;
        }

        // 判断转账类型
        if (fromAccount.getAccountTypeDebitCredit() == 0) {
            // 转出账户是信用卡，设为提现
            transferType = "提现";
        } else if (toAccount.getAccountTypeDebitCredit() == 0) {
            // 转入账户是信用卡，设为还款
            transferType = "还款";
        } else if (toAccount.getAccountTypeName() != null &&
                toAccount.getAccountTypeName().contains("现金")) {
            // 转入账户是现金，设为提现
            transferType = "提现";
        } else {
            // 其他情况为普通转账
            transferType = "转账";
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        recordingTransferViewBinding = null;

        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    // 实现RecordingDataProvider接口
    @Override
    public TransactionCategory getSelectedCategory() {
        return null; // 转账页面不需要分类
    }

    @Override
    public TransactionSubcategory getSelectedSubcategory() {
        return null; // 转账页面不需要子分类
    }

    @Override
    public Account getFromAccount() {
        return fromAccount;
    }

    @Override
    public Account getToAccount() {
        return toAccount;
    }

    @Override
    public String getTransferType() {
        return transferType;
    }

    @Override
    public boolean hasRequiredData() {
        return fromAccount != null && toAccount != null;
    }
}
