package com.example.likeqianwang.ui.main_wallets;

import android.app.Application;
import android.util.Log;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Repository.AccountRepository;

import java.util.List;

public class WalletsViewModel extends AndroidViewModel {

    private final AccountRepository accountRepository;
    private LiveData<List<Account>> allAccounts;
    private final LiveData<List<Account>> debitAccounts;
    private final LiveData<List<Account>> creditAccounts;
    private List<Account> cachedAccounts;

    public WalletsViewModel(Application application) {
        super(application);
        accountRepository = new AccountRepository(application);
        allAccounts = accountRepository.getAllAccounts();
        debitAccounts = accountRepository.getDebitAccounts();
        creditAccounts = accountRepository.getCreditAccounts();
    }

    public LiveData<List<Account>> getAllAccounts() {
        return allAccounts;
    }

    public LiveData<List<Account>> getDebitAccounts() {
        return debitAccounts;
    }

    public LiveData<List<Account>> getCreditAccounts() {
        return creditAccounts;
    }

    public LiveData<List<Account>> getAccountsByCategory(String category) {
        return accountRepository.getAccountsByCategory(category);
    }

    // 新增预加载方法
    public void preloadAccounts() {
        new Thread(() -> {
            try {
                cachedAccounts = accountRepository.getAllAccountsSync();
            } catch (Exception e) {
                Log.e("WalletsViewModel", "预加载账户数据失败", e);
            }
        }).start();
    }

    // 添加删除账户的方法
    public void deleteAccount(Account account) {
        new Thread(() -> {
            accountRepository.delete(account);
        }).start();
    }

    // 获取缓存的账户数据
    public List<Account> getCachedAccounts() {
        return cachedAccounts;
    }
}