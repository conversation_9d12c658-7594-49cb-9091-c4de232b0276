package com.example.likeqianwang.ui.recording_include_in_stats_budget;

import android.app.Dialog;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.DialogIncludeInStatsBudgetViewBinding;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.ShapeAppearanceModel;

public class RecordingIncludeInStatsBudgetDialog extends BottomSheetDialogFragment {
    private RecordingIncludeInStatsBudgetListener statsBudgetListener;
    private DialogIncludeInStatsBudgetViewBinding dialogIncludeInStatsBudgetViewBinding;
    private boolean includeInStats = true; // 默认计入收支
    private boolean includeInBudget = true; // 默认计入预算

    public interface RecordingIncludeInStatsBudgetListener {
        void includeInStatsBudgetSelected(boolean includeInStats, boolean includeInBudget);
    }

    public static RecordingIncludeInStatsBudgetDialog newInstance(boolean currentIncludeInStats, boolean currentIncludeInBudget) {
        RecordingIncludeInStatsBudgetDialog recordingIncludeInStatsBudgetDialog = new RecordingIncludeInStatsBudgetDialog();
        Bundle includeArgs = new Bundle();
        includeArgs.putBoolean("includeInStats", currentIncludeInStats);
        includeArgs.putBoolean("includeInBudget", currentIncludeInBudget);
        recordingIncludeInStatsBudgetDialog.setArguments(includeArgs);
        return recordingIncludeInStatsBudgetDialog;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        try {
            // 尝试将上下文转换为监听器
            if (getParentFragment() instanceof RecordingIncludeInStatsBudgetListener) {
                statsBudgetListener = (RecordingIncludeInStatsBudgetListener) getParentFragment();
            } else if (context instanceof RecordingIncludeInStatsBudgetListener) {
                statsBudgetListener = (RecordingIncludeInStatsBudgetListener) context;
            } else {
                throw new ClassCastException(context + " 必须实现 RecordingIncludeInStatsBudgetListener");
            }
        } catch (ClassCastException e) {
            throw new ClassCastException(context + " 必须实现 RecordingIncludeInStatsBudgetListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置自定义样式
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);

        if (getArguments() != null) {
            includeInStats = getArguments().getBoolean("includeInStats", true);
            includeInBudget = getArguments().getBoolean("includeInBudget", true);
        }

        // 设置对话框背景半透明
        setDialogBackgroundDim();
    }

    // 设置对话框背景半透明
    private void setDialogBackgroundDim() {
        // 方法1：使用主题设置
        if (getDialog() != null) {
            getDialog().getWindow().setDimAmount(0.5f); // 设置背景暗度（0-1，0为全透明，1为全黑）
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置对话框显示时为展开状态
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置圆角背景
                setupBottomSheetBackground(bottomSheet);
            }
        });

        return dialog;
    }

    // 设置底部表单的圆角背景
    private void setupBottomSheetBackground(View bottomSheet) {
        // 创建圆角形状
        ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel.Builder()
                .setTopRightCorner(CornerFamily.ROUNDED, dpToPx(16))
                .setTopLeftCorner(CornerFamily.ROUNDED, dpToPx(16))
                .build();

        // 创建MaterialShapeDrawable
        MaterialShapeDrawable shapeDrawable = new MaterialShapeDrawable(shapeAppearanceModel);
        shapeDrawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        shapeDrawable.setElevation(dpToPx(8));
        shapeDrawable.setShadowColor(Color.LTGRAY);

        // 设置背景
        bottomSheet.setBackground(shapeDrawable);
    }

    private float dpToPx(int dp) {
        return dp * getResources().getDisplayMetrics().density;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        dialogIncludeInStatsBudgetViewBinding = DialogIncludeInStatsBudgetViewBinding.inflate(inflater, container, false);

        // 设置点击事件
        dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInStats.setOnClickListener(v -> dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInStats.toggle());

        dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInBudget.setOnClickListener(v -> dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInBudget.toggle());

        // 设置确认按钮点击事件
        dialogIncludeInStatsBudgetViewBinding.IncludeInStatsBudgetConfirm.setOnClickListener(v -> {
            if (statsBudgetListener != null) {
                // 注意：CheckedTextView的isChecked()为true表示"不计入"，所以需要取反
                boolean newIncludeInStats = !dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInStats.isChecked();
                boolean newIncludeInBudget = !dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInBudget.isChecked();
                statsBudgetListener.includeInStatsBudgetSelected(newIncludeInStats, newIncludeInBudget);
            }
            dismiss();
        });

        // 设置取消按钮点击事件
        dialogIncludeInStatsBudgetViewBinding.IncludeInStatsBudgetCancel.setOnClickListener(v -> dismiss());

        return dialogIncludeInStatsBudgetViewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 根据传入的参数设置初始状态
        // 注意：CheckedTextView的isChecked()为true表示"不计入"，所以需要取反
        dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInStats.setChecked(!includeInStats);
        dialogIncludeInStatsBudgetViewBinding.recordingPageIncludeInBudget.setChecked(!includeInBudget);

        // 设置对话框的样式，使其占满屏幕宽度
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
        }

        // 设置背景半透明
        View parent = (View) view.getParent();
        parent.setBackgroundColor(Color.TRANSPARENT);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        dialogIncludeInStatsBudgetViewBinding = null;
    }

}
