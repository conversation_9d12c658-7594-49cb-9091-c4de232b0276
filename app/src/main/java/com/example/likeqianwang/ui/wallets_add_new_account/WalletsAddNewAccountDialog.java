package com.example.likeqianwang.ui.wallets_add_new_account;

import android.app.Dialog;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.likeqianwang.DataModel.AccountTypeCategory;
import com.example.likeqianwang.DataModel.AccountTypeItem;
import com.example.likeqianwang.FillNewAccountInfoActivity;
import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.AccountTypeCategory_adapter;
import com.example.likeqianwang.databinding.DialogAddNewAccountListViewBinding;
import com.example.likeqianwang.ui.wallets_bank_seleciton.WalletsBankSelectionDialog;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class WalletsAddNewAccountDialog extends BottomSheetDialogFragment implements AccountTypeCategory_adapter.OnCategoryInteractionListener {
    private DialogAddNewAccountListViewBinding dialogAddNewAccountListViewBinding;
    private AccountTypeCategory_adapter accountTypeCategoryAdapter;
    private List<AccountTypeCategory> categoryList;
    private OnAccountTypeSelectedListener onAccountTypeSelectedListener;
    private boolean shouldStartNewActivity = true;

    public interface OnAccountTypeSelectedListener {
        void onAccountTypeSelected(AccountTypeItem accountType);
    }

    public static WalletsAddNewAccountDialog newInstance() {
        return new WalletsAddNewAccountDialog();
    }

    public void setOnAccountTypeSelectedListener(OnAccountTypeSelectedListener onAccountTypeSelectedListener) {
        this.onAccountTypeSelectedListener = onAccountTypeSelectedListener;
    }

    public void setShouldStartNewActivity(boolean shouldStartNewActivity) {
        this.shouldStartNewActivity = shouldStartNewActivity;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置自定义样式
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);

        // 设置对话框背景半透明
        setDialogBackgroundDim();
    }

    // 设置对话框背景半透明
    private void setDialogBackgroundDim() {
        // 方法1：使用主题设置
        if (getDialog() != null) {
            getDialog().getWindow().setDimAmount(0.5f); // 设置背景暗度（0-1，0为全透明，1为全黑）
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置对话框显示时为展开状态
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);

                // 设置固定高度为800dp
                ViewGroup.LayoutParams layoutParams = bottomSheet.getLayoutParams();
                layoutParams.height = (int) (800 * getResources().getDisplayMetrics().density);
                bottomSheet.setLayoutParams(layoutParams);

                // 禁用拖动，保持固定高度
                behavior.setDraggable(false);

                // 确保对话框紧贴屏幕底部
                behavior.setPeekHeight(layoutParams.height);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置圆角背景
                setupBottomSheetBackground(bottomSheet);
            }
        });

        return dialog;
    }

    // 设置底部表单的圆角背景
    private void setupBottomSheetBackground(View bottomSheet) {
        // 创建圆角形状
        ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel.Builder()
                .setTopRightCorner(CornerFamily.ROUNDED, dpToPx(16))
                .setTopLeftCorner(CornerFamily.ROUNDED, dpToPx(16))
                .build();

        // 创建MaterialShapeDrawable
        MaterialShapeDrawable shapeDrawable = new MaterialShapeDrawable(shapeAppearanceModel);
        shapeDrawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        shapeDrawable.setElevation(dpToPx(8));
        shapeDrawable.setShadowColor(Color.LTGRAY);

        // 设置背景
        bottomSheet.setBackground(shapeDrawable);
    }

    private float dpToPx(int dp) {
        return dp * getResources().getDisplayMetrics().density;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        dialogAddNewAccountListViewBinding = DialogAddNewAccountListViewBinding.inflate(inflater, container, false);
        return dialogAddNewAccountListViewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 设置返回按钮点击事件
        dialogAddNewAccountListViewBinding.walletsAccountBack.setOnClickListener(v -> dismiss());

        // 准备数据
        prepareData();

        // 设置RecyclerView
        dialogAddNewAccountListViewBinding.walletsAddAccountType.setLayoutManager(new LinearLayoutManager(requireContext()));
        accountTypeCategoryAdapter = new AccountTypeCategory_adapter(requireContext(), categoryList, this);
        dialogAddNewAccountListViewBinding.walletsAddAccountType.setAdapter(accountTypeCategoryAdapter);

        // 设置对话框的样式，使其占满屏幕宽度，高度固定为800dp
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) (800 * getResources().getDisplayMetrics().density)
            );
        }

        // 设置背景半透明
        View parent = (View) view.getParent();
        parent.setBackgroundColor(Color.TRANSPARENT);
    }

    private void prepareData() {
        categoryList = new ArrayList<>();

        // 创建一些示例数据
        List<AccountTypeItem> cashItems = Arrays.asList(
                new AccountTypeItem("cash_1", "现金", R.drawable.account_icon_cash, "cat_cash",1),
                new AccountTypeItem("cash_2", "微信支付", R.drawable.account_icon_wechat, "cat_cash",1),
                new AccountTypeItem("cash_3", "支付宝", R.drawable.account_icon_alipay, "cat_cash",1)
        );

        List<AccountTypeItem> bankItems = Arrays.asList(
                new AccountTypeItem("bank_1", "储蓄卡", R.drawable.account_icon_debit_card, "cat_bank",1),
                new AccountTypeItem("bank_2", "信用卡", R.drawable.account_icon_credit_card, "cat_bank",0)
        );

        List<AccountTypeItem> digitalItems = Arrays.asList(
                new AccountTypeItem("digital_1", "花呗", R.drawable.account_icon_huabei, "cat_digital",0),
                new AccountTypeItem("digital_2", "京东白条", R.drawable.account_icon_jd, "cat_digital",0),
                new AccountTypeItem("digital_3", "数字人民币", R.drawable.account_icon_e_cny, "cat_digital",1)
        );

        categoryList.add(new AccountTypeCategory("现金账户", cashItems, "cat_cash"));
        categoryList.add(new AccountTypeCategory("银行账户", bankItems, "cat_bank"));
        categoryList.add(new AccountTypeCategory("数字账户", digitalItems, "cat_digital"));
    }

    @Override
    public void onCategoryClick(AccountTypeCategory category, int position) {

    }

    @Override
    public void onItemClick(AccountTypeCategory category, AccountTypeItem item, int categoryPosition, int itemPosition) {
        // 当用户选择了一个账户类型
        if (onAccountTypeSelectedListener != null) {
            onAccountTypeSelectedListener.onAccountTypeSelected(item);
        }

        // 检查是否是银行卡类型
        if (item.getParentAccountTypeCategoryId().equals("cat_bank")) {
            // 如果是银行卡类型，显示银行选择对话框
            if (shouldStartNewActivity) {
                // 如果需要启动新Activity，则创建WalletsBankSelectionDialog
                WalletsBankSelectionDialog bankDialog = WalletsBankSelectionDialog.newInstance();

                // 传递账户类型信息到银行选择对话框
                Bundle args = new Bundle();
                args.putString("ACCOUNT_TYPE_ID", item.getId());
                args.putString("ACCOUNT_TYPE_NAME", item.getName());
                args.putString("ACCOUNT_TYPE_CATEGORY_ID", item.getParentAccountTypeCategoryId());
                args.putInt("ACCOUNT_TYPE_DEBIT_CREDIT", item.getDebitOrCredit());
                bankDialog.setArguments(args);

                bankDialog.show(getParentFragmentManager(), "bank_selection_dialog");
            }
        } else if (shouldStartNewActivity) { // 根据标志决定是否启动新Activity
            // 创建Intent跳转到FillNewAccountInfoActivity
            Intent intent = new Intent(getContext(), FillNewAccountInfoActivity.class);
            intent.putExtra("ACCOUNT_TYPE_ID", item.getId());
            intent.putExtra("ACCOUNT_TYPE_NAME", item.getName());
            intent.putExtra("ACCOUNT_TYPE_ICON", item.getIconResId());
            intent.putExtra("ACCOUNT_TYPE_CATEGORY_ID", item.getParentAccountTypeCategoryId());
            intent.putExtra("ACCOUNT_TYPE_DEBIT_CREDIT", item.getDebitOrCredit());

            startActivity(intent);
        }

        dismiss(); // 选择后关闭对话框

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        dialogAddNewAccountListViewBinding = null;
    }
}
