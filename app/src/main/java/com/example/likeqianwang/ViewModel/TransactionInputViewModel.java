package com.example.likeqianwang.ViewModel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.Repository.TransactionRepository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.inject.Inject;

import dagger.hilt.android.lifecycle.HiltViewModel;

@HiltViewModel
public class TransactionInputViewModel extends ViewModel {
    private final TransactionRepository repository;
    private final MutableLiveData<Boolean> saveStatus = new MutableLiveData<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    @Inject
    public TransactionInputViewModel(TransactionRepository repository) {
        this.repository = repository;
    }

    // 保存交易记录
    public void saveTransaction(String type,
                                Date date,
                                BigDecimal amount,
                                String currencySymbol,
                                long categoryId,
                                String fromAccountId,
                                String toAccountId,  // 转账时使用
                                String remark,
                                List<Long> tagIds,
                                boolean includeInStats,
                                boolean includeInBudget) {
        executorService.execute(() -> {
            try {
                // 创建交易记录
                Transactions transaction = new Transactions();
                transaction.setType(type);
                transaction.setTransactionDate(date);
                transaction.setAmount(amount);
                transaction.setCurrencySymbol(currencySymbol);
                transaction.setCategoryId(categoryId);
                transaction.setFromAccountId(fromAccountId);
                transaction.setToAccountId(toAccountId);
                transaction.setRemark(remark);
                transaction.setIncludeInStats(includeInStats);
                transaction.setIncludeInBudget(includeInBudget);

                // 保存交易记录
                repository.addTransaction(transaction);

                // 通知保存成功
                saveStatus.postValue(true);
            } catch (Exception e) {
                // 通知保存失败
                saveStatus.postValue(false);
            }
        });
    }

    // 获取保存状态
    public LiveData<Boolean> getSaveStatus() {
        return saveStatus;
    }

}
