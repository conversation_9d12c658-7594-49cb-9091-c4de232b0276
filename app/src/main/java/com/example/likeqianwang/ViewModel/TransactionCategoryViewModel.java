package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Repository.TransactionCategoryRepository;

import java.util.List;

public class TransactionCategoryViewModel extends AndroidViewModel {
    private final TransactionCategoryRepository repository;
    private final MutableLiveData<List<TransactionCategory>> expenseCategories = new MutableLiveData<>();
    private final MutableLiveData<List<TransactionCategory>> incomeCategories = new MutableLiveData<>();

    public TransactionCategoryViewModel(@NonNull Application application) {
        super(application);
        repository = new TransactionCategoryRepository(application);
        loadCategories();
    }

    private void loadCategories() {
        // 加载支出分类
        repository.loadCategoriesWithSubcategories(0, categories -> {
            expenseCategories.postValue(categories);
        });

        // 加载收入分类
        repository.loadCategoriesWithSubcategories(1, categories -> {
            incomeCategories.postValue(categories);
        });
    }

    public LiveData<List<TransactionCategory>> getExpenseCategories() {
        return expenseCategories;
    }

    public LiveData<List<TransactionCategory>> getIncomeCategories() {
        return incomeCategories;
    }
}
