package com.example.likeqianwang.Entity;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.example.likeqianwang.Utils.Converters;

import java.util.Date;
import java.util.UUID;

@Entity(tableName = "transaction_accounts", indices = @Index(value = "accountName", unique = true))
@TypeConverters(Converters.class)
public class Account {
    @PrimaryKey
    @NonNull
    private String accountId;

    // 账户基本信息
    private String accountName;         // 账户名称
    private String accountTypeId;       // 账户类型ID
    private String accountTypeName;     // 账户类型名称
    private int accountTypeIcon;        // 账户类型图标
    private String accountTypeCategoryId; // 账户类型分类
    private int accountTypeDebitCredit; // 账户类型：1-资产，0-负债

    // 银行信息（如果有）
    private String bankId;              // 银行ID
    private String bankName;            // 银行名称
    private int bankIcon;               // 银行图标

    // 账户余额信息
    private double accountBalance;      // 账户余额（借记卡）或当前欠款（信用卡）
    private double totalCredit;         // 信用卡总额度

    // 信用卡特有信息
    private int statementDate;          // 账单日
    private int dueDate;                // 还款日
    private boolean dueDateInCurrentPeriod; // 出账日账单计入当期

    // 其他信息
    private String currencySymbol;      // 账户币种
    private boolean includeInAsset;     // 是否计入总资产
    private String accountRemark;       // 账户备注

    // 记录信息
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间

    public Account() {
        this.accountId = UUID.randomUUID().toString();
        this.createTime = new Date();
        this.updateTime = new Date();
        this.currencySymbol = "CNY";          // 默认人民币
        this.includeInAsset = true;     // 默认计入总资产
    }

    @NonNull
    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(@NonNull String accountId) {
        this.accountId = accountId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountTypeId() {
        return accountTypeId;
    }

    public void setAccountTypeId(String accountTypeId) {
        this.accountTypeId = accountTypeId;
    }

    public String getAccountTypeName() {
        return accountTypeName;
    }

    public void setAccountTypeName(String accountTypeName) {
        this.accountTypeName = accountTypeName;
    }

    public int getAccountTypeIcon() {
        return accountTypeIcon;
    }

    public void setAccountTypeIcon(int accountTypeIcon) {
        this.accountTypeIcon = accountTypeIcon;
    }

    public String getAccountTypeCategoryId() {
        return accountTypeCategoryId;
    }

    public void setAccountTypeCategoryId(String accountTypeCategoryId) {
        this.accountTypeCategoryId = accountTypeCategoryId;
    }

    public int getAccountTypeDebitCredit() {
        return accountTypeDebitCredit;
    }

    public void setAccountTypeDebitCredit(int accountTypeDebitCredit) {
        this.accountTypeDebitCredit = accountTypeDebitCredit;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public int getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(int bankIcon) {
        this.bankIcon = bankIcon;
    }

    public double getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(double accountBalance) {
        this.accountBalance = accountBalance;
    }

    public double getTotalCredit() {
        return totalCredit;
    }

    public void setTotalCredit(double totalCredit) {
        this.totalCredit = totalCredit;
    }

    public int getStatementDate() {
        return statementDate;
    }

    public void setStatementDate(int statementDate) {
        this.statementDate = statementDate;
    }

    public int getDueDate() {
        return dueDate;
    }

    public void setDueDate(int dueDate) {
        this.dueDate = dueDate;
    }

    public boolean isDueDateInCurrentPeriod() {
        return dueDateInCurrentPeriod;
    }

    public void setDueDateInCurrentPeriod(boolean dueDateInCurrentPeriod) {
        this.dueDateInCurrentPeriod = dueDateInCurrentPeriod;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public boolean isIncludeInAsset() {
        return includeInAsset;
    }

    public void setIncludeInAsset(boolean includeInAsset) {
        this.includeInAsset = includeInAsset;
    }

    public String getAccountRemark() {
        return accountRemark;
    }

    public void setAccountRemark(String accountRemark) {
        this.accountRemark = accountRemark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
